File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,21.974499%,11857,96,11306,551,0,96
oled.o,6.234478%,3364,22,1270,2072,22,0
date_take.o,5.039104%,2719,39,2472,241,6,33
usart_app.o,4.883428%,2635,517,2540,95,0,517
sdio_sdcard.o,4.460877%,2407,45,2406,0,1,44
btod.o,3.988287%,2152,0,2152,0,0,0
head.o,3.250676%,1754,576,1752,0,2,574
ebtn.o,3.109826%,1678,60,1678,0,0,60
fz_wm.l,2.920790%,1576,0,1560,16,0,0
scanf_fp.o,2.357389%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.949665%,1052,0,1052,0,0,0
perf_counter.o,1.805108%,974,76,962,4,8,68
date_save.o,1.688350%,911,0,784,127,0,0
gd32f4xx_rcu.o,1.497461%,808,0,800,8,0,0
m_wm.l,1.486341%,802,0,802,0,0,0
_printf_fp_hex.o,1.486341%,802,0,764,38,0,0
scanf_hexfp.o,1.482635%,800,0,800,0,0,0
syscheck.o,1.206494%,651,0,598,53,0,0
gd32f4xx_timer.o,1.082323%,584,0,584,0,0,0
system_gd32f4xx.o,1.023018%,552,4,548,0,4,0
gd32f4xx_usart.o,0.985952%,532,0,532,0,0,0
gd32f4xx_adc.o,0.963713%,520,0,520,0,0,0
gd32f4xx_fmc.o,0.960006%,518,0,518,0,0,0
startup_gd32f450_470.o,0.911820%,492,2048,64,428,0,2048
gd32f4xx_dma.o,0.867341%,468,0,468,0,0,0
btn_app.o,0.822862%,444,196,234,14,196,0
gd32f4xx_rtc.o,0.808036%,436,0,436,0,0,0
gd32f4xx_i2c.o,0.804329%,434,0,434,0,0,0
gd32f4xx_sdio.o,0.763557%,412,0,412,0,0,0
__printf_flags_ss_wp.o,0.757997%,409,0,392,17,0,0
bigflt0.o,0.696838%,376,0,228,148,0,0
gd25qxx.o,0.659772%,356,0,356,0,0,0
dmul.o,0.630120%,340,0,340,0,0,0
lc_ctype_c.o,0.585641%,316,0,44,272,0,0
scanf_infnan.o,0.570814%,308,0,308,0,0,0
gd32f4xx_it.o,0.533748%,288,5,288,0,0,5
gd32f4xx_gpio.o,0.522629%,282,0,282,0,0,0
narrow.o,0.492976%,266,0,266,0,0,0
lludivv7m.o,0.444790%,240,0,240,0,0,0
ldexp.o,0.422551%,228,0,228,0,0,0
_printf_wctomb.o,0.363245%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.348419%,188,0,148,40,0,0
gd32f4xx_dac.o,0.337299%,182,0,182,0,0,0
_printf_intcommon.o,0.329886%,178,0,178,0,0,0
strtod.o,0.326180%,176,0,176,0,0,0
scheduler.o,0.318766%,172,85,88,0,84,1
led_app.o,0.302087%,163,7,162,0,1,6
gd32f4xx_misc.o,0.300234%,162,0,162,0,0,0
_strtoul.o,0.292820%,158,0,158,0,0,0
dnaninf.o,0.289114%,156,0,156,0,0,0
strncmp.o,0.277994%,150,0,150,0,0,0
frexp.o,0.259461%,140,0,140,0,0,0
fnaninf.o,0.259461%,140,0,140,0,0,0
rt_memcpy_v6.o,0.255754%,138,0,138,0,0,0
lludiv10.o,0.255754%,138,0,138,0,0,0
_printf_fp_infnan.o,0.237222%,128,0,128,0,0,0
perfc_port_default.o,0.233515%,126,0,126,0,0,0
strcmpv7em.o,0.229808%,124,0,124,0,0,0
_printf_longlong_dec.o,0.229808%,124,0,124,0,0,0
dleqf.o,0.222395%,120,0,120,0,0,0
deqf.o,0.222395%,120,0,120,0,0,0
_printf_dec.o,0.222395%,120,0,120,0,0,0
systick.o,0.218689%,118,4,118,0,0,4
llsdiv.o,0.214982%,116,0,116,0,0,0
strtol.o,0.207569%,112,0,112,0,0,0
_printf_oct_int_ll.o,0.207569%,112,0,112,0,0,0
drleqf.o,0.200156%,108,0,108,0,0,0
gd32f4xx_spi.o,0.189036%,102,0,102,0,0,0
retnan.o,0.185329%,100,0,100,0,0,0
rt_memcpy_w.o,0.185329%,100,0,100,0,0,0
d2f.o,0.181623%,98,0,98,0,0,0
__scatter.o,0.174210%,94,0,94,0,0,0
main.o,0.174210%,94,0,94,0,0,0
scalbn.o,0.170503%,92,0,92,0,0,0
__dczerorl2.o,0.166796%,90,0,90,0,0,0
memcmp.o,0.163090%,88,0,88,0,0,0
f2d.o,0.159383%,86,0,86,0,0,0
strncpy.o,0.159383%,86,0,86,0,0,0
_printf_str.o,0.151970%,82,0,82,0,0,0
rt_memclr_w.o,0.144557%,78,0,78,0,0,0
_printf_pad.o,0.144557%,78,0,78,0,0,0
sys_stackheap_outer.o,0.137144%,74,0,74,0,0,0
lc_numeric_c.o,0.133437%,72,0,44,28,0,0
_c16rtomb.o,0.133437%,72,0,72,0,0,0
rt_memclr.o,0.126024%,68,0,68,0,0,0
dunder.o,0.118611%,64,0,64,0,0,0
_sgetc.o,0.118611%,64,0,64,0,0,0
strlen.o,0.114904%,62,0,62,0,0,0
vsnprintf.o,0.111198%,60,0,60,0,0,0
oled_app.o,0.111198%,60,0,60,0,0,0
atof.o,0.103784%,56,0,56,0,0,0
fpclassify.o,0.088958%,48,0,48,0,0,0
trapv.o,0.088958%,48,0,48,0,0,0
_printf_char_common.o,0.088958%,48,0,48,0,0,0
init_aeabi.o,0.081545%,44,0,44,0,0,0
_printf_wchar.o,0.081545%,44,0,44,0,0,0
_printf_char.o,0.081545%,44,0,44,0,0,0
__2sprintf.o,0.081545%,44,0,44,0,0,0
_printf_charcount.o,0.074132%,40,0,40,0,0,0
dflt_clz.o,0.070425%,38,0,38,0,0,0
llshl.o,0.070425%,38,0,38,0,0,0
libinit2.o,0.070425%,38,0,38,0,0,0
_printf_truncate.o,0.066719%,36,0,36,0,0,0
_chval.o,0.051892%,28,0,28,0,0,0
__scatter_zi.o,0.051892%,28,0,28,0,0,0
systick_wrapper_gnu.o,0.051892%,28,0,28,0,0,0
fpinit.o,0.048186%,26,0,26,0,0,0
atoi.o,0.048186%,26,0,26,0,0,0
dcmpi.o,0.044479%,24,0,24,0,0,0
_rserrno.o,0.040772%,22,0,22,0,0,0
adc_app.o,0.040772%,22,0,22,0,0,0
isspace.o,0.033359%,18,0,18,0,0,0
exit.o,0.033359%,18,0,18,0,0,0
gd32f4xx_pmu.o,0.033359%,18,0,18,0,0,0
fpconst.o,0.029653%,16,0,0,16,0,0
dcheck1.o,0.029653%,16,0,16,0,0,0
rt_ctype_table.o,0.029653%,16,0,16,0,0,0
_snputc.o,0.029653%,16,0,16,0,0,0
__printf_wp.o,0.025946%,14,0,14,0,0,0
dretinf.o,0.022240%,12,0,12,0,0,0
sys_exit.o,0.022240%,12,0,12,0,0,0
__rtentry2.o,0.022240%,12,0,12,0,0,0
rtc_app.o,0.022240%,12,0,12,0,0,0
fretinf.o,0.018533%,10,0,10,0,0,0
rtexit2.o,0.018533%,10,0,10,0,0,0
_sputc.o,0.018533%,10,0,10,0,0,0
_printf_ll.o,0.018533%,10,0,10,0,0,0
_printf_l.o,0.018533%,10,0,10,0,0,0
sd_app.o,0.018533%,10,0,10,0,0,0
scanf2.o,0.014826%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.014826%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.014826%,8,0,8,0,0,0
libspace.o,0.014826%,8,96,8,0,0,96
__main.o,0.014826%,8,0,8,0,0,0
istatus.o,0.011120%,6,0,6,0,0,0
heapauxi.o,0.011120%,6,0,6,0,0,0
_printf_x.o,0.011120%,6,0,6,0,0,0
_printf_u.o,0.011120%,6,0,6,0,0,0
_printf_s.o,0.011120%,6,0,6,0,0,0
_printf_p.o,0.011120%,6,0,6,0,0,0
_printf_o.o,0.011120%,6,0,6,0,0,0
_printf_n.o,0.011120%,6,0,6,0,0,0
_printf_ls.o,0.011120%,6,0,6,0,0,0
_printf_llx.o,0.011120%,6,0,6,0,0,0
_printf_llu.o,0.011120%,6,0,6,0,0,0
_printf_llo.o,0.011120%,6,0,6,0,0,0
_printf_lli.o,0.011120%,6,0,6,0,0,0
_printf_lld.o,0.011120%,6,0,6,0,0,0
_printf_lc.o,0.011120%,6,0,6,0,0,0
_printf_i.o,0.011120%,6,0,6,0,0,0
_printf_g.o,0.011120%,6,0,6,0,0,0
_printf_f.o,0.011120%,6,0,6,0,0,0
_printf_e.o,0.011120%,6,0,6,0,0,0
_printf_d.o,0.011120%,6,0,6,0,0,0
_printf_c.o,0.011120%,6,0,6,0,0,0
_printf_a.o,0.011120%,6,0,6,0,0,0
__rtentry4.o,0.011120%,6,0,6,0,0,0
scanf1.o,0.007413%,4,0,4,0,0,0
printf2.o,0.007413%,4,0,4,0,0,0
printf1.o,0.007413%,4,0,4,0,0,0
_printf_percent_end.o,0.007413%,4,0,4,0,0,0
use_no_semi.o,0.003707%,2,0,2,0,0,0
rtexit.o,0.003707%,2,0,2,0,0,0
libshutdown2.o,0.003707%,2,0,2,0,0,0
libshutdown.o,0.003707%,2,0,2,0,0,0
libinit.o,0.003707%,2,0,2,0,0,0
