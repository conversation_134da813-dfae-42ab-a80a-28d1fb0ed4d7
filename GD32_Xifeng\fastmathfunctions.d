gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/FastMathFunctions.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_cos_f32.c
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/fast_math_functions.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h
gd32_xifeng\fastmathfunctions.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
gd32_xifeng\fastmathfunctions.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/none.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/utils.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/basic_math_functions.h
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_cos_q15.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_cos_q31.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_sin_f32.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_sin_q15.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_sin_q31.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_sqrt_q31.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_sqrt_q15.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_vexp_f32.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_vexp_f64.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_vlog_f32.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_vlog_f64.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_divide_q15.c
gd32_xifeng\fastmathfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_divide_q31.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_vlog_q31.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_vlog_q15.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_atan2_f32.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_atan2_q31.c
gd32_xifeng\fastmathfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/arm_atan2_q15.c
