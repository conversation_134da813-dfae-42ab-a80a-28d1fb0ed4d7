<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [GD32_Xifeng\GD32_Xifeng.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image GD32_Xifeng\GD32_Xifeng.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Mon May 12 17:34:14 2025
<BR><P>
<H3>Maximum Stack Usage =      18720 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
cmd_ls &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[4f]">CAN1_RX0_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4f]">CAN1_RX0_IRQHandler</a><BR>
 <LI><a href="#[35]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[35]">BusFault_Handler</a><BR>
 <LI><a href="#[33]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[33]">HardFault_Handler</a><BR>
 <LI><a href="#[34]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[34]">MemManage_Handler</a><BR>
 <LI><a href="#[32]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[32]">NMI_Handler</a><BR>
 <LI><a href="#[172]">UART_EndTxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[172]">UART_EndTxTransfer</a><BR>
 <LI><a href="#[173]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[173]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[36]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[36]">UsageFault_Handler</a><BR>
 <LI><a href="#[24e]">mem_cmp</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[24e]">mem_cmp</a><BR>
 <LI><a href="#[237]">lfs_file_write</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[237]">lfs_file_write</a><BR>
 <LI><a href="#[231]">lfs_file_read</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2a6]">lfs_file_flush</a><BR>
 <LI><a href="#[2ad]">lfs_dir_traverse</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2ad]">lfs_dir_traverse</a><BR>
 <LI><a href="#[2a3]">lfs_dir_commit</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2ab]">lfs_dir_drop</a><BR>
 <LI><a href="#[2bd]">lfs_fs_relocate</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2a3]">lfs_dir_commit</a><BR>
 <LI><a href="#[2ba]">lfs_dir_split</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2b4]">lfs_dir_compact</a><BR>
 <LI><a href="#[28d]">list_dir_recursive</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[28d]">list_dir_recursive</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[98]">ADC_DMAConvCplt</a> from stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[9a]">ADC_DMAError</a> from stm32f4xx_hal_adc.o(i.ADC_DMAError) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[99]">ADC_DMAHalfConvCplt</a> from stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[4d]">ADC_IRQHandler</a> from stm32f4xx_it.o(i.ADC_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[35]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7b]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7c]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7d]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7a]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[9e]">DAC_DMAConvCpltCh1</a> from stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[9b]">DAC_DMAConvCpltCh2</a> from stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[a0]">DAC_DMAErrorCh1</a> from stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[9d]">DAC_DMAErrorCh2</a> from stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[9f]">DAC_DMAHalfConvCpltCh1</a> from stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[9c]">DAC_DMAHalfConvCpltCh2</a> from stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) referenced from stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
 <LI><a href="#[89]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[94]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[73]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[74]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[75]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[76]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[77]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[80]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[81]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[78]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[79]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[52]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6b]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8b]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8a]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">I2C1_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[84]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[83]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[93]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[92]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a3]">LeftWheelMenu_CallBack</a> from oled_app.o(i.LeftWheelMenu_CallBack) referenced from oled_app.o(i.PIDMenu_Init)
 <LI><a href="#[a2]">MainMenu_CallBack</a> from oled_app.o(i.MainMenu_CallBack) referenced from oled_app.o(i.PIDMenu_Init)
 <LI><a href="#[34]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[bb]">OLED_SendBuff</a> from oled_app.o(i.OLED_SendBuff) referenced from main.o(i.main)
 <LI><a href="#[7e]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[65]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[86]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[85]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[88]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[87]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a5]">ParamAdjust_CallBack</a> from oled_app.o(i.ParamAdjust_CallBack) referenced from oled_app.o(i.PIDMenu_Init)
 <LI><a href="#[39]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[64]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a4]">RightWheelMenu_CallBack</a> from oled_app.o(i.RightWheelMenu_CallBack) referenced from oled_app.o(i.PIDMenu_Init)
 <LI><a href="#[91]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6c]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[c1]">SD_initialize</a> from sd_diskio.o(i.SD_initialize) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[c5]">SD_ioctl</a> from sd_diskio.o(i.SD_ioctl) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[c3]">SD_read</a> from sd_diskio.o(i.SD_read) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[c2]">SD_status</a> from sd_diskio.o(i.SD_status) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[c4]">SD_write</a> from sd_diskio.o(i.SD_write) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[5e]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">SPI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6e]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8e]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8f]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[90]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[37]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[96]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[3d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[59]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6d]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[71]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[72]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[69]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[68]">TIM8_TRG_COM_TIM14_IRQHandler</a> from stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[67]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6f]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[70]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8c]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8d]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a1]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[a8]">UART_DMAError</a> from stm32f4xx_hal_uart.o(i.UART_DMAError) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[a6]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[a7]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[60]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">USART2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">USART3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[82]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[36]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[17]">WouoUI_ConfWinPageIn</a> from wououi_win.o(i.WouoUI_ConfWinPageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[18]">WouoUI_ConfWinPageInParaInit</a> from wououi_win.o(i.WouoUI_ConfWinPageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1b]">WouoUI_ConfWinPageIndicatorCtrl</a> from wououi_win.o(i.WouoUI_ConfWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1a]">WouoUI_ConfWinPageReact</a> from wououi_win.o(i.WouoUI_ConfWinPageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[19]">WouoUI_ConfWinPageShow</a> from wououi_win.o(i.WouoUI_ConfWinPageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[6]">WouoUI_FuncDoNothing</a> from wououi.o(i.WouoUI_FuncDoNothing) referenced 12 times from wououi.o(.data)
 <LI><a href="#[6]">WouoUI_FuncDoNothing</a> from wououi.o(i.WouoUI_FuncDoNothing) referenced from wououi.o(i.WouoUI_ScrollBarProc)
 <LI><a href="#[d]">WouoUI_FuncDoNothingRetTrue</a> from wououi.o(i.WouoUI_FuncDoNothingRetTrue) referenced 2 times from wououi.o(.data)
 <LI><a href="#[7]">WouoUI_ListPageIn</a> from wououi_page.o(i.WouoUI_ListPageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[8]">WouoUI_ListPageInParaInit</a> from wououi_page.o(i.WouoUI_ListPageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[b]">WouoUI_ListPageIndicatorCtrl</a> from wououi_page.o(i.WouoUI_ListPageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[a]">WouoUI_ListPageReact</a> from wououi_page.o(i.WouoUI_ListPageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[c]">WouoUI_ListPageScrollBarCtrl</a> from wououi_page.o(i.WouoUI_ListPageScrollBarCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[9]">WouoUI_ListPageShow</a> from wououi_page.o(i.WouoUI_ListPageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[26]">WouoUI_ListWinPageIn</a> from wououi_win.o(i.WouoUI_ListWinPageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[27]">WouoUI_ListWinPageInParaInit</a> from wououi_win.o(i.WouoUI_ListWinPageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[2a]">WouoUI_ListWinPageIndicatorCtrl</a> from wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[29]">WouoUI_ListWinPageReact</a> from wououi_win.o(i.WouoUI_ListWinPageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[2b]">WouoUI_ListWinPageScrollBarCtrl</a> from wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[28]">WouoUI_ListWinPageShow</a> from wououi_win.o(i.WouoUI_ListWinPageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[12]">WouoUI_MsgWinPageIn</a> from wououi_win.o(i.WouoUI_MsgWinPageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[13]">WouoUI_MsgWinPageInParaInit</a> from wououi_win.o(i.WouoUI_MsgWinPageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[16]">WouoUI_MsgWinPageIndicatorCtrl</a> from wououi_win.o(i.WouoUI_MsgWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[15]">WouoUI_MsgWinPageReact</a> from wououi_win.o(i.WouoUI_MsgWinPageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[14]">WouoUI_MsgWinPageShow</a> from wououi_win.o(i.WouoUI_MsgWinPageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[21]">WouoUI_SpinWinPageIn</a> from wououi_win.o(i.WouoUI_SpinWinPageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[22]">WouoUI_SpinWinPageInParaInit</a> from wououi_win.o(i.WouoUI_SpinWinPageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[25]">WouoUI_SpinWinPageIndicatorCtrl</a> from wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[24]">WouoUI_SpinWinPageReact</a> from wououi_win.o(i.WouoUI_SpinWinPageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[23]">WouoUI_SpinWinPageShow</a> from wououi_win.o(i.WouoUI_SpinWinPageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1]">WouoUI_TitlePageIn</a> from wououi_page.o(i.WouoUI_TitlePageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[2]">WouoUI_TitlePageInParaInit</a> from wououi_page.o(i.WouoUI_TitlePageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[5]">WouoUI_TitlePageIndicatorCtrl</a> from wououi_page.o(i.WouoUI_TitlePageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[4]">WouoUI_TitlePageReact</a> from wououi_page.o(i.WouoUI_TitlePageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[3]">WouoUI_TitlePageShow</a> from wououi_page.o(i.WouoUI_TitlePageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1c]">WouoUI_ValWinPageIn</a> from wououi_win.o(i.WouoUI_ValWinPageIn) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1d]">WouoUI_ValWinPageInParaInit</a> from wououi_win.o(i.WouoUI_ValWinPageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[20]">WouoUI_ValWinPageIndicatorCtrl</a> from wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1f]">WouoUI_ValWinPageReact</a> from wououi_win.o(i.WouoUI_ValWinPageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[1e]">WouoUI_ValWinPageShow</a> from wououi_win.o(i.WouoUI_ValWinPageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[e]">WouoUI_WavePageInParaInit</a> from wououi_page.o(i.WouoUI_WavePageInParaInit) referenced 2 times from wououi.o(.data)
 <LI><a href="#[11]">WouoUI_WavePageIndicatorCtrl</a> from wououi_page.o(i.WouoUI_WavePageIndicatorCtrl) referenced 2 times from wououi.o(.data)
 <LI><a href="#[10]">WouoUI_WavePageReact</a> from wououi_page.o(i.WouoUI_WavePageReact) referenced 2 times from wououi.o(.data)
 <LI><a href="#[f]">WouoUI_WavePageShow</a> from wououi_page.o(i.WouoUI_WavePageShow) referenced 2 times from wououi.o(.data)
 <LI><a href="#[97]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[aa]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[aa]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[ab]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[2d]">adc_task</a> from adc_app.o(i.adc_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[2e]">btn_task</a> from btn_app.o(i.btn_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[cd]">cmd_cat</a> from shell_app.o(i.cmd_cat) referenced from shell_app.o(.constdata)
 <LI><a href="#[cb]">cmd_cd</a> from shell_app.o(i.cmd_cd) referenced from shell_app.o(.constdata)
 <LI><a href="#[d4]">cmd_clear</a> from shell_app.o(i.cmd_clear) referenced from shell_app.o(.constdata)
 <LI><a href="#[d2]">cmd_cp</a> from shell_app.o(i.cmd_cp) referenced from shell_app.o(.constdata)
 <LI><a href="#[d3]">cmd_echo</a> from shell_app.o(i.cmd_echo) referenced from shell_app.o(.constdata)
 <LI><a href="#[c9]">cmd_help</a> from shell_app.o(i.cmd_help) referenced from shell_app.o(.constdata)
 <LI><a href="#[ca]">cmd_ls</a> from shell_app.o(i.cmd_ls) referenced from shell_app.o(.constdata)
 <LI><a href="#[ce]">cmd_mkdir</a> from shell_app.o(i.cmd_mkdir) referenced from shell_app.o(.constdata)
 <LI><a href="#[d1]">cmd_mv</a> from shell_app.o(i.cmd_mv) referenced from shell_app.o(.constdata)
 <LI><a href="#[cc]">cmd_pwd</a> from shell_app.o(i.cmd_pwd) referenced from shell_app.o(.constdata)
 <LI><a href="#[cf]">cmd_rm</a> from shell_app.o(i.cmd_rm) referenced from shell_app.o(.constdata)
 <LI><a href="#[d0]">cmd_touch</a> from shell_app.o(i.cmd_touch) referenced from shell_app.o(.constdata)
 <LI><a href="#[d5]">cmd_write</a> from shell_app.o(i.cmd_write) referenced from shell_app.o(.constdata)
 <LI><a href="#[a9]">fputc</a> from usart.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[2c]">led_task</a> from led_app.o(i.led_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[ae]">lfs_alloc_lookahead</a> from lfs.o(i.lfs_alloc_lookahead) referenced from lfs.o(i.lfs_alloc)
 <LI><a href="#[b7]">lfs_deskio_erase</a> from lfs_port.o(i.lfs_deskio_erase) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[b6]">lfs_deskio_prog</a> from lfs_port.o(i.lfs_deskio_prog) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[b5]">lfs_deskio_read</a> from lfs_port.o(i.lfs_deskio_read) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[b8]">lfs_deskio_sync</a> from lfs_port.o(i.lfs_deskio_sync) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[af]">lfs_dir_commit_commit</a> from lfs.o(i.lfs_dir_commit_commit) referenced from lfs.o(i.lfs_dir_commit)
 <LI><a href="#[af]">lfs_dir_commit_commit</a> from lfs.o(i.lfs_dir_commit_commit) referenced from lfs.o(i.lfs_dir_compact)
 <LI><a href="#[b0]">lfs_dir_commit_size</a> from lfs.o(i.lfs_dir_commit_size) referenced from lfs.o(i.lfs_dir_compact)
 <LI><a href="#[b1]">lfs_dir_find_match</a> from lfs.o(i.lfs_dir_find_match) referenced from lfs.o(i.lfs_dir_find)
 <LI><a href="#[b1]">lfs_dir_find_match</a> from lfs.o(i.lfs_dir_find_match) referenced from lfs.o(i.lfs_mount)
 <LI><a href="#[b2]">lfs_dir_traverse_filter</a> from lfs.o(i.lfs_dir_traverse_filter) referenced from lfs.o(i.lfs_dir_traverse)
 <LI><a href="#[b3]">lfs_fs_parent_match</a> from lfs.o(i.lfs_fs_parent_match) referenced from lfs.o(i.lfs_fs_parent)
 <LI><a href="#[b4]">lfs_fs_size_count</a> from lfs.o(i.lfs_fs_size_count) referenced from lfs.o(i.lfs_fs_size)
 <LI><a href="#[95]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[30]">oled_task</a> from oled_app.o(i.oled_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[ac]">prv_btn_event</a> from btn_app.o(i.prv_btn_event) referenced from btn_app.o(i.app_btn_init)
 <LI><a href="#[ad]">prv_btn_get_state</a> from btn_app.o(i.prv_btn_get_state) referenced from btn_app.o(i.app_btn_init)
 <LI><a href="#[c8]">u8g2_draw_l90_r0</a> from u8g2_setup.o(i.u8g2_draw_l90_r0) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[bc]">u8g2_font_calc_vref_font</a> from u8g2_font.o(i.u8g2_font_calc_vref_font) referenced from u8g2_font.o(i.u8g2_SetFontPosBaseline)
 <LI><a href="#[b9]">u8g2_gpio_and_delay_stm32</a> from oled_app.o(i.u8g2_gpio_and_delay_stm32) referenced from main.o(i.main)
 <LI><a href="#[bf]">u8g2_ll_hvline_vertical_top_lsb</a> from u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) referenced from u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
 <LI><a href="#[c6]">u8g2_update_dimension_r0</a> from u8g2_setup.o(i.u8g2_update_dimension_r0) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[c7]">u8g2_update_page_win_r0</a> from u8g2_setup.o(i.u8g2_update_page_win_r0) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[ba]">u8x8_byte_hw_i2c</a> from oled_app.o(i.u8x8_byte_hw_i2c) referenced from main.o(i.main)
 <LI><a href="#[bd]">u8x8_cad_ssd13xx_fast_i2c</a> from u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) referenced from u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
 <LI><a href="#[be]">u8x8_d_ssd1306_128x32_univision</a> from u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_univision) referenced from u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
 <LI><a href="#[c0]">u8x8_dummy_cb</a> from u8x8_setup.o(i.u8x8_dummy_cb) referenced from u8x8_setup.o(i.u8x8_SetupDefaults)
 <LI><a href="#[2f]">uart_task</a> from usart_app.o(i.uart_task) referenced 2 times from scheduler.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[97]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[318]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[d6]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[ed]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[319]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[31a]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[31b]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[31c]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[31d]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[31]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[92]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[d8]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
<LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_adc_timer_frequency
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1a0]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SendBuff
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[115]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Map_FFT_To_Input_Frequency
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Info
</UL>

<P><STRONG><a name="[31e]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffClear
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[31f]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[320]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[db]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffClear
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>

<P><STRONG><a name="[11f]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_init
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
</UL>

<P><STRONG><a name="[321]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[dd]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[2ed]"></a>strcat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, strcat.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
</UL>

<P><STRONG><a name="[238]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_execute
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_add_to_history
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
</UL>

<P><STRONG><a name="[1e5]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[1df]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_history
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_add_to_history
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_write
</UL>

<P><STRONG><a name="[198]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_execute
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_add_to_history
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MainMenu_CallBack
</UL>

<P><STRONG><a name="[229]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[2ec]"></a>strncat</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, strncat.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncat
</UL>
<BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
</UL>

<P><STRONG><a name="[235]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f_str
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_history
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
</UL>

<P><STRONG><a name="[2ea]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
</UL>

<P><STRONG><a name="[2e7]"></a>strtok</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, strtok.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_execute
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>

<P><STRONG><a name="[2eb]"></a>strrchr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, strrchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
</UL>

<P><STRONG><a name="[2c0]"></a>strspn</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, strspn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strspn
</UL>
<BR>[Called By]<UL><LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[2c1]"></a>strcspn</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, strcspn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcspn
</UL>
<BR>[Called By]<UL><LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[de]"></a>__aeabi_fadd</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_inverse_f32
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_f32
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
</UL>

<P><STRONG><a name="[e1]"></a>__aeabi_fsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_inverse_f32
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_f32
</UL>

<P><STRONG><a name="[e2]"></a>__aeabi_frsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[f3]"></a>__aeabi_fmul</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_inverse_f32
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_f32
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
</UL>

<P><STRONG><a name="[e3]"></a>__aeabi_ul2f</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, ffltul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_ul2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_get_adc_sampling_interval_us
</UL>

<P><STRONG><a name="[221]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[322]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[21d]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[da]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[323]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[d9]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[324]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[325]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[e0]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[df]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2f
</UL>

<P><STRONG><a name="[e4]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[e8]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[e9]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[ea]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[eb]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ec]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[21a]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d7]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[326]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[e5]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[327]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[e7]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[e6]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[328]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[329]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[f1]"></a>arm_bitreversal_f32</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, arm_bitreversal.o(.text.arm_bitreversal_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = arm_bitreversal_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>

<P><STRONG><a name="[ee]"></a>arm_cfft_radix4_f32</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_inverse_f32
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix4_butterfly_f32
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitreversal_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Phase
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Frequency_And_Type
</UL>

<P><STRONG><a name="[19a]"></a>arm_cfft_radix4_init_f32</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32))
<BR><BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_FFT_Init
</UL>

<P><STRONG><a name="[f2]"></a>arm_cmplx_mag_f32</STRONG> (Thumb, 232 bytes, Stack size 32 bytes, arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = arm_cmplx_mag_f32 &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Frequency_And_Type
</UL>

<P><STRONG><a name="[f0]"></a>arm_radix4_butterfly_f32</STRONG> (Thumb, 1384 bytes, Stack size 176 bytes, arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = arm_radix4_butterfly_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>

<P><STRONG><a name="[ef]"></a>arm_radix4_butterfly_inverse_f32</STRONG> (Thumb, 1434 bytes, Stack size 176 bytes, arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
</UL>

<P><STRONG><a name="[4d]"></a>ADC_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ADC_IRQHandler &rArr; HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[f9]"></a>Analyze_Frequency_And_Type</STRONG> (Thumb, 440 bytes, Stack size 40 bytes, waveform_analyzer_app.o(i.Analyze_Frequency_And_Type))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = Analyze_Frequency_And_Type &rArr; arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Map_FFT_To_Input_Frequency
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_get_adc_sampling_interval_us
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Info
</UL>

<P><STRONG><a name="[fc]"></a>Analyze_Harmonics</STRONG> (Thumb, 654 bytes, Stack size 4160 bytes, waveform_analyzer_app.o(i.Analyze_Harmonics))
<BR><BR>[Stack]<UL><LI>Max Depth = 4380<LI>Call Chain = Analyze_Harmonics &rArr; arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Map_Input_To_FFT_Frequency
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Map_FFT_To_Input_Frequency
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Component_Phase
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calculate_Phase_Difference
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_get_adc_sampling_interval_us
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Info
</UL>

<P><STRONG><a name="[100]"></a>BSP_SD_GetCardInfo</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, bsp_driver_sd.o(i.BSP_SD_GetCardInfo))
<BR><BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ioctl
</UL>

<P><STRONG><a name="[102]"></a>BSP_SD_GetCardState</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_driver_sd.o(i.BSP_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = BSP_SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_CheckStatus
</UL>

<P><STRONG><a name="[104]"></a>BSP_SD_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, bsp_driver_sd.o(i.BSP_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = BSP_SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[105]"></a>BSP_SD_IsDetected</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, bsp_driver_sd.o(i.BSP_SD_IsDetected))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = BSP_SD_IsDetected
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[108]"></a>BSP_SD_ReadBlocks</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, bsp_driver_sd.o(i.BSP_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
</UL>

<P><STRONG><a name="[10a]"></a>BSP_SD_WriteBlocks</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, bsp_driver_sd.o(i.BSP_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
</UL>

<P><STRONG><a name="[35]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[ff]"></a>Calculate_Phase_Difference</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, waveform_analyzer_app.o(i.Calculate_Phase_Difference))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
</UL>

<P><STRONG><a name="[9e]"></a>DAC_DMAConvCpltCh1</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAConvCpltCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConvCpltCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[9b]"></a>DAC_DMAConvCpltCh2</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAConvCpltCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ConvCpltCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[a0]"></a>DAC_DMAErrorCh1</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAErrorCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ErrorCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[9d]"></a>DAC_DMAErrorCh2</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAErrorCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ErrorCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[9f]"></a>DAC_DMAHalfConvCpltCh1</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAHalfConvCpltCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConvHalfCpltCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[9c]"></a>DAC_DMAHalfConvCpltCh2</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DMAHalfConvCpltCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ConvHalfCpltCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[4b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[122]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[18c]"></a>FATFS_LinkDriver</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ff_gen_drv.o(i.FATFS_LinkDriver))
<BR><BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
</UL>

<P><STRONG><a name="[32a]"></a>FATFS_LinkDriverEx</STRONG> (Thumb, 64 bytes, Stack size 4 bytes, ff_gen_drv.o(i.FATFS_LinkDriverEx), UNUSED)

<P><STRONG><a name="[220]"></a>GetWaveformTypeString</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, waveform_analyzer_app.o(i.GetWaveformTypeString))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[fe]"></a>Get_Component_Phase</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, waveform_analyzer_app.o(i.Get_Component_Phase))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Get_Component_Phase &rArr; __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Phase
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
</UL>

<P><STRONG><a name="[114]"></a>Get_Waveform_Info</STRONG> (Thumb, 92 bytes, Stack size 72 bytes, waveform_analyzer_app.o(i.Get_Waveform_Info))
<BR><BR>[Stack]<UL><LI>Max Depth = 4452<LI>Call Chain = Get_Waveform_Info &rArr; Analyze_Harmonics &rArr; arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Vpp
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Phase
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Frequency_And_Type
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[117]"></a>Get_Waveform_Phase</STRONG> (Thumb, 178 bytes, Stack size 16 bytes, waveform_analyzer_app.o(i.Get_Waveform_Phase))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = Get_Waveform_Phase &rArr; arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_f32
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Map_Input_To_FFT_Frequency
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Component_Phase
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_get_adc_sampling_interval_us
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Info
</UL>

<P><STRONG><a name="[116]"></a>Get_Waveform_Vpp</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, waveform_analyzer_app.o(i.Get_Waveform_Vpp))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Get_Waveform_Vpp &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Info
</UL>

<P><STRONG><a name="[11a]"></a>HAL_ADCEx_InjectedConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[187]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 334 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[f6]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, adc_app.o(i.HAL_ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[f7]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[f5]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[f8]"></a>HAL_ADC_IRQHandler</STRONG> (Thumb, 302 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_LevelOutOfWindowCallback
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[11c]"></a>HAL_ADC_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[11b]"></a>HAL_ADC_LevelOutOfWindowCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[11d]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 174 bytes, Stack size 40 bytes, adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[125]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 302 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[119]"></a>HAL_ADC_Stop_DMA</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[10d]"></a>HAL_DACEx_ConvCpltCallbackCh2</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAConvCpltCh2
</UL>

<P><STRONG><a name="[111]"></a>HAL_DACEx_ConvHalfCpltCallbackCh2</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAHalfConvCpltCh2
</UL>

<P><STRONG><a name="[10f]"></a>HAL_DACEx_ErrorCallbackCh2</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAErrorCh2
</UL>

<P><STRONG><a name="[189]"></a>HAL_DAC_ConfigChannel</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DAC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[10c]"></a>HAL_DAC_ConvCpltCallbackCh1</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAConvCpltCh1
</UL>

<P><STRONG><a name="[110]"></a>HAL_DAC_ConvHalfCpltCallbackCh1</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAHalfConvCpltCh1
</UL>

<P><STRONG><a name="[10e]"></a>HAL_DAC_ErrorCallbackCh1</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAErrorCh1
</UL>

<P><STRONG><a name="[128]"></a>HAL_DAC_Init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[129]"></a>HAL_DAC_MspInit</STRONG> (Thumb, 138 bytes, Stack size 40 bytes, dac.o(i.HAL_DAC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
</UL>

<P><STRONG><a name="[12a]"></a>HAL_DAC_Start_DMA</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_DAC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
</UL>

<P><STRONG><a name="[12b]"></a>HAL_DAC_Stop_DMA</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_DAC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
</UL>

<P><STRONG><a name="[127]"></a>HAL_DMA_Abort</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Stop_DMA
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[176]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[112]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 412 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
</UL>

<P><STRONG><a name="[121]"></a>HAL_DMA_Init</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[126]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start_DMA
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[130]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_gpio_and_delay_stm32
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[120]"></a>HAL_GPIO_Init</STRONG> (Thumb, 510 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[2e1]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_get_state
</UL>

<P><STRONG><a name="[18e]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
<LI><a href="#[2f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>

<P><STRONG><a name="[12c]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[2df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[190]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[191]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[131]"></a>HAL_I2C_Init</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[134]"></a>HAL_I2C_Master_Transmit</STRONG> (Thumb, 290 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_hw_i2c
</UL>

<P><STRONG><a name="[139]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 294 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[132]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[1be]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[13b]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13d]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[13e]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[124]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>

<P><STRONG><a name="[123]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[13c]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[141]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[133]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[1c1]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[142]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[143]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 856 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[107]"></a>HAL_SD_ConfigWideBusOperation</STRONG> (Thumb, 276 bytes, Stack size 40 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[1bc]"></a>HAL_SD_GetCardCSD</STRONG> (Thumb, 402 bytes, Stack size 20 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_SD_GetCardCSD
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[101]"></a>HAL_SD_GetCardInfo</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>

<P><STRONG><a name="[103]"></a>HAL_SD_GetCardState</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>

<P><STRONG><a name="[106]"></a>HAL_SD_Init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[14c]"></a>HAL_SD_InitCard</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_ON
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[14b]"></a>HAL_SD_MspInit</STRONG> (Thumb, 130 bytes, Stack size 48 bytes, sdio.o(i.HAL_SD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[109]"></a>HAL_SD_ReadBlocks</STRONG> (Thumb, 476 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
</UL>

<P><STRONG><a name="[10b]"></a>HAL_SD_WriteBlocks</STRONG> (Thumb, 428 bytes, Stack size 88 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
</UL>

<P><STRONG><a name="[158]"></a>HAL_SPI_Init</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[159]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[15a]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 496 bytes, Stack size 56 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[13f]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[169]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[16b]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[195]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[15c]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
</UL>

<P><STRONG><a name="[15d]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[224]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start))
<BR><BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_adc_timer_frequency
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
</UL>

<P><STRONG><a name="[227]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[2f6]"></a>HAL_TIM_Base_Stop</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop))
<BR><BR>[Called By]<UL><LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_adc_timer_frequency
<LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
</UL>

<P><STRONG><a name="[15f]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_ETR_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[317]"></a>HAL_TIM_GenerateEvent</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent))
<BR><BR>[Called By]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
<LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_adc_timer_frequency
</UL>

<P><STRONG><a name="[165]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[164]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 304 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_TRG_COM_TIM14_IRQHandler
</UL>

<P><STRONG><a name="[166]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[167]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[168]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, btn_app.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_TIM_PeriodElapsedCallback &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[16a]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[16d]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[16f]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, usart_app.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[170]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[177]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[174]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 618 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[179]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[17a]"></a>HAL_UART_MspInit</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[17c]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT))
<BR><BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[17e]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, usart_app.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[1c0]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[17f]"></a>HAL_UART_Transmit</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[178]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[33]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a3]"></a>LeftWheelMenu_CallBack</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, oled_app.o(i.LeftWheelMenu_CallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LeftWheelMenu_CallBack &rArr; WouoUI_JumpToPage &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_JumpToPage
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageSetMinStepMax
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListTitlePageGetSelectOpt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> oled_app.o(i.PIDMenu_Init)
</UL>
<P><STRONG><a name="[186]"></a>MX_ADC1_Init</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, adc.o(i.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[188]"></a>MX_DAC_Init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, dac.o(i.MX_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_DAC_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18a]"></a>MX_DMA_Init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18b]"></a>MX_FATFS_Init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, fatfs.o(i.MX_FATFS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MX_FATFS_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FATFS_LinkDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18d]"></a>MX_GPIO_Init</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18f]"></a>MX_I2C1_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2d4]"></a>MX_SDIO_SD_Init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sdio.o(i.MX_SDIO_SD_Init))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[192]"></a>MX_SPI2_Init</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, spi.o(i.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[193]"></a>MX_TIM14_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, tim.o(i.MX_TIM14_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = MX_TIM14_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[194]"></a>MX_TIM3_Init</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[196]"></a>MX_TIM6_Init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, tim.o(i.MX_TIM6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = MX_TIM6_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[197]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a2]"></a>MainMenu_CallBack</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, oled_app.o(i.MainMenu_CallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = MainMenu_CallBack &rArr; WouoUI_JumpToPage &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_JumpToPage
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListTitlePageGetSelectOpt
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> oled_app.o(i.PIDMenu_Init)
</UL>
<P><STRONG><a name="[fb]"></a>Map_FFT_To_Input_Frequency</STRONG> (Thumb, 182 bytes, Stack size 120 bytes, waveform_analyzer_app.o(i.Map_FFT_To_Input_Frequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Map_FFT_To_Input_Frequency
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Frequency_And_Type
</UL>

<P><STRONG><a name="[fd]"></a>Map_Input_To_FFT_Frequency</STRONG> (Thumb, 196 bytes, Stack size 0 bytes, waveform_analyzer_app.o(i.Map_Input_To_FFT_Frequency))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Phase
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
</UL>

<P><STRONG><a name="[34]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[199]"></a>My_FFT_Init</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, waveform_analyzer_app.o(i.My_FFT_Init))
<BR><BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix4_init_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[32]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[19b]"></a>OLED_Clear</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[19e]"></a>OLED_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bb]"></a>OLED_SendBuff</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, oled_app.o(i.OLED_SendBuff))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_SendBuff &rArr; u8g2_SendBuffer &rArr; u8g2_send_buffer &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[19f]"></a>OLED_Set_Position</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[19c]"></a>OLED_Write_cmd</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[19d]"></a>OLED_Write_data</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[1a2]"></a>PIDMenu_Init</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, oled_app.o(i.PIDMenu_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = PIDMenu_Init &rArr; WouoUI_ValWinPageInit &rArr; WouoUI_ValWinPageSetMinStepMax &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SelectDefaultUI
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffSend
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffClear
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInit
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInit
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageInit
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a5]"></a>ParamAdjust_CallBack</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, oled_app.o(i.ParamAdjust_CallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ParamAdjust_CallBack &rArr; WouoUI_ListTitlePageGetSelectOpt &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageValIncrease
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageValDecrease
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListTitlePageGetSelectOpt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> oled_app.o(i.PIDMenu_Init)
</UL>
<P><STRONG><a name="[39]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a4]"></a>RightWheelMenu_CallBack</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, oled_app.o(i.RightWheelMenu_CallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = RightWheelMenu_CallBack &rArr; WouoUI_JumpToPage &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_JumpToPage
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageSetMinStepMax
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListTitlePageGetSelectOpt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> oled_app.o(i.PIDMenu_Init)
</UL>
<P><STRONG><a name="[150]"></a>SDIO_ConfigData</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[1bb]"></a>SDIO_GetPowerState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState))
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[144]"></a>SDIO_GetResponse</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[148]"></a>SDIO_Init</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[14d]"></a>SDIO_PowerState_ON</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[153]"></a>SDIO_ReadFIFO</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[1ac]"></a>SDIO_SendCommand</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand))
<BR><BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
</UL>

<P><STRONG><a name="[157]"></a>SDIO_WriteFIFO</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[146]"></a>SDMMC_CmdAppCommand</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdAppCommand &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[1ae]"></a>SDMMC_CmdAppOperCommand</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdAppOperCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp3
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[149]"></a>SDMMC_CmdBlockLength</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdBlockLength &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[147]"></a>SDMMC_CmdBusWidth</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdBusWidth &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[1b0]"></a>SDMMC_CmdGoIdleState</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_CmdGoIdleState
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[1b1]"></a>SDMMC_CmdOperCond</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdOperCond
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp7
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[151]"></a>SDMMC_CmdReadMultiBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdReadMultiBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[152]"></a>SDMMC_CmdReadSingleBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdReadSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[1b3]"></a>SDMMC_CmdSelDesel</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdSelDesel &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[1b4]"></a>SDMMC_CmdSendCID</STRONG> (Thumb, 44 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdSendCID
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[1b6]"></a>SDMMC_CmdSendCSD</STRONG> (Thumb, 44 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdSendCSD
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[1b7]"></a>SDMMC_CmdSendSCR</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[14a]"></a>SDMMC_CmdSendStatus</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>

<P><STRONG><a name="[1b8]"></a>SDMMC_CmdSetRelAdd</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[154]"></a>SDMMC_CmdStopTransfer</STRONG> (Thumb, 46 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[155]"></a>SDMMC_CmdWriteMultiBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdWriteMultiBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[156]"></a>SDMMC_CmdWriteSingleBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[1ad]"></a>SDMMC_GetCmdResp1</STRONG> (Thumb, 278 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
</UL>

<P><STRONG><a name="[1b5]"></a>SDMMC_GetCmdResp2</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2))
<BR><BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
</UL>

<P><STRONG><a name="[1af]"></a>SDMMC_GetCmdResp3</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3))
<BR><BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
</UL>

<P><STRONG><a name="[1b9]"></a>SDMMC_GetCmdResp6</STRONG> (Thumb, 130 bytes, Stack size 12 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
</UL>

<P><STRONG><a name="[1b2]"></a>SDMMC_GetCmdResp7</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7))
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
</UL>

<P><STRONG><a name="[c1]"></a>SD_initialize</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, sd_diskio.o(i.SD_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = SD_initialize &rArr; BSP_SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_CheckStatus
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[c5]"></a>SD_ioctl</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, sd_diskio.o(i.SD_ioctl))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SD_ioctl
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[c3]"></a>SD_read</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, sd_diskio.o(i.SD_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = SD_read &rArr; BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[c2]"></a>SD_status</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sd_diskio.o(i.SD_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = SD_status &rArr; SD_CheckStatus &rArr; BSP_SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_CheckStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[c4]"></a>SD_write</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, sd_diskio.o(i.SD_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = SD_write &rArr; BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[37]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1bf]"></a>SystemClock_Config</STRONG> (Thumb, 142 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[68]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = TIM8_TRG_COM_TIM14_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15e]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 170 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[160]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[16e]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[17d]"></a>UART_Start_Receive_IT</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>

<P><STRONG><a name="[60]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1d5]"></a>WouoUI_Animation</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, wououi_anim.o(i.WouoUI_Animation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_Animation
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageShow
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIn
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageShow
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIn
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ScrollBarProc
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_IndicatorProc
</UL>

<P><STRONG><a name="[1c2]"></a>WouoUI_AttachSendBuffFun</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, wououi.o(i.WouoUI_AttachSendBuffFun))
<BR><BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetSendBuffFun
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e1]"></a>WouoUI_BlurParaInit</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, wououi.o(i.WouoUI_BlurParaInit))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_JumpToPage
</UL>

<P><STRONG><a name="[1c4]"></a>WouoUI_BlurProc</STRONG> (Thumb, 162 bytes, Stack size 16 bytes, wououi.o(i.WouoUI_BlurProc))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = WouoUI_BlurProc &rArr; WouoUI_BuffAllBlur &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>

<P><STRONG><a name="[1c5]"></a>WouoUI_BuffAllBlur</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, wououi_graph.o(i.WouoUI_BuffAllBlur))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = WouoUI_BuffAllBlur &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BlurProc
</UL>

<P><STRONG><a name="[1a4]"></a>WouoUI_BuffClear</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_BuffClear))
<BR><BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>

<P><STRONG><a name="[1a5]"></a>WouoUI_BuffSend</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_BuffSend))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>

<P><STRONG><a name="[1c7]"></a>WouoUI_CanvasDrawASCII</STRONG> (Thumb, 418 bytes, Stack size 56 bytes, wououi_graph.o(i.WouoUI_CanvasDrawASCII))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>

<P><STRONG><a name="[1c9]"></a>WouoUI_CanvasDrawBMP</STRONG> (Thumb, 212 bytes, Stack size 56 bytes, wououi_graph.o(i.WouoUI_CanvasDrawBMP))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = WouoUI_CanvasDrawBMP &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageShow
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIn
</UL>

<P><STRONG><a name="[1ca]"></a>WouoUI_CanvasDrawBoxRightAngle</STRONG> (Thumb, 194 bytes, Stack size 56 bytes, wououi_graph.o(i.WouoUI_CanvasDrawBoxRightAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = WouoUI_CanvasDrawBoxRightAngle &rArr; WouoUI_CanvasDrawLine_V &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIndicatorCtrl
</UL>

<P><STRONG><a name="[1cb]"></a>WouoUI_CanvasDrawLine_H</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, wououi_graph.o(i.WouoUI_CanvasDrawLine_H))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBoxRightAngle
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageScrollBarCtrl
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageScrollBarCtrl
</UL>

<P><STRONG><a name="[1cc]"></a>WouoUI_CanvasDrawLine_V</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, wououi_graph.o(i.WouoUI_CanvasDrawLine_V))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = WouoUI_CanvasDrawLine_V &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBoxRightAngle
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageScrollBarCtrl
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageScrollBarCtrl
</UL>

<P><STRONG><a name="[1cd]"></a>WouoUI_CanvasDrawPoint</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_CanvasDrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = WouoUI_CanvasDrawPoint &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
</UL>

<P><STRONG><a name="[1ce]"></a>WouoUI_CanvasDrawRBox</STRONG> (Thumb, 18 bytes, Stack size 32 bytes, wououi_graph.o(i.WouoUI_CanvasDrawRBox))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIndicatorCtrl
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageShow
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIn
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIndicatorCtrl
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIn
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageScrollBarCtrl
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIndicatorCtrl
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageScrollBarCtrl
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIndicatorCtrl
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIndicatorCtrl
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[1cf]"></a>WouoUI_CanvasDrawRBoxCommon</STRONG> (Thumb, 176 bytes, Stack size 56 bytes, wououi_graph.o(i.WouoUI_CanvasDrawRBoxCommon))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
</UL>

<P><STRONG><a name="[1d0]"></a>WouoUI_CanvasDrawRBoxEmpty</STRONG> (Thumb, 18 bytes, Stack size 32 bytes, wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = WouoUI_CanvasDrawRBoxEmpty &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageIndicatorCtrl
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIndicatorCtrl
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[1d1]"></a>WouoUI_CanvasDrawSlideStr</STRONG> (Thumb, 214 bytes, Stack size 40 bytes, wououi_graph.o(i.WouoUI_CanvasDrawSlideStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>

<P><STRONG><a name="[1d2]"></a>WouoUI_CanvasDrawStr</STRONG> (Thumb, 72 bytes, Stack size 48 bytes, wououi_graph.o(i.WouoUI_CanvasDrawStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>

<P><STRONG><a name="[1d4]"></a>WouoUI_CanvasDrawStrAutoNewline</STRONG> (Thumb, 122 bytes, Stack size 64 bytes, wououi_graph.o(i.WouoUI_CanvasDrawStrAutoNewline))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIn
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>

<P><STRONG><a name="[1d9]"></a>WouoUI_CanvasSlideStrReset</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_CanvasSlideStrReset))
<BR><BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInParaInit
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInParaInit
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageInParaInit
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageInParaInit
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageInParaInit
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageToggleBtn
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageLastItem
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TItlePageNextItem
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageNextItem
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageLastItem
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveAnimParaReaset
</UL>

<P><STRONG><a name="[17]"></a>WouoUI_ConfWinPageIn</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ConfWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = WouoUI_ConfWinPageIn &rArr; _WouoUI_ConfWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[18]"></a>WouoUI_ConfWinPageInParaInit</STRONG> (Thumb, 334 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ConfWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ConfWinPageInParaInit &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrHeightAutoNewLine
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1b]"></a>WouoUI_ConfWinPageIndicatorCtrl</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_ConfWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = WouoUI_ConfWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1a]"></a>WouoUI_ConfWinPageReact</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ConfWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ConfWinPageReact &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageToggleBtn
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageSlideUpTxt
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageSlideDownTxt
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[19]"></a>WouoUI_ConfWinPageShow</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ConfWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = WouoUI_ConfWinPageShow &rArr; _WouoUI_ConfWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1dc]"></a>WouoUI_ConfWinPageSlideDownTxt</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_ConfWinPageSlideDownTxt))
<BR><BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
</UL>

<P><STRONG><a name="[1db]"></a>WouoUI_ConfWinPageSlideUpTxt</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, wououi_win.o(i.WouoUI_ConfWinPageSlideUpTxt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_ConfWinPageSlideUpTxt
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
</UL>

<P><STRONG><a name="[1dd]"></a>WouoUI_ConfWinPageToggleBtn</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_ConfWinPageToggleBtn))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_ConfWinPageToggleBtn &rArr; WouoUI_GetStrWidth
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
</UL>

<P><STRONG><a name="[6]"></a>WouoUI_FuncDoNothing</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wououi.o(i.WouoUI_FuncDoNothing))
<BR>[Address Reference Count : 2]<UL><LI> wououi.o(i.WouoUI_ScrollBarProc)
<LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[d]"></a>WouoUI_FuncDoNothingRetTrue</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, wououi.o(i.WouoUI_FuncDoNothingRetTrue))
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1d8]"></a>WouoUI_GetStrHeightAutoNewLine</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, wououi_graph.o(i.WouoUI_GetStrHeightAutoNewLine))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_GetStrHeightAutoNewLine
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageInParaInit
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
</UL>

<P><STRONG><a name="[1d3]"></a>WouoUI_GetStrWidth</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, wououi_graph.o(i.WouoUI_GetStrWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_GetStrWidth
</UL>
<BR>[Calls]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIndicatorCtrl
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageToggleBtn
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>

<P><STRONG><a name="[1f6]"></a>WouoUI_GraphSetBuff</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_GraphSetBuff))
<BR><BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SetCurrentUI
</UL>

<P><STRONG><a name="[1f7]"></a>WouoUI_GraphSetPen</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_GraphSetPen))
<BR><BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SetCurrentUI
</UL>

<P><STRONG><a name="[1a6]"></a>WouoUI_GraphSetPenColor</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_GraphSetPenColor))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIndicatorCtrl
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIndicatorCtrl
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIndicatorCtrl
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIn
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIndicatorCtrl
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIndicatorCtrl
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIndicatorCtrl
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BlurProc
</UL>

<P><STRONG><a name="[1c3]"></a>WouoUI_GraphSetSendBuffFun</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, wououi_graph.o(i.WouoUI_GraphSetSendBuffFun))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_AttachSendBuffFun
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SetCurrentUI
</UL>

<P><STRONG><a name="[1e0]"></a>WouoUI_IndicatorProc</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, wououi.o(i.WouoUI_IndicatorProc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_IndicatorProc &rArr; WouoUI_Animation
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>

<P><STRONG><a name="[185]"></a>WouoUI_JumpToPage</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, wououi.o(i.WouoUI_JumpToPage))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WouoUI_JumpToPage &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BlurParaInit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RightWheelMenu_CallBack
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MainMenu_CallBack
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeftWheelMenu_CallBack
</UL>

<P><STRONG><a name="[7]"></a>WouoUI_ListPageIn</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, wououi_page.o(i.WouoUI_ListPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 340<LI>Call Chain = WouoUI_ListPageIn &rArr; WouoUI_ListDrawText_CheckBox &rArr; WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>WouoUI_ListPageInParaInit</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, wououi_page.o(i.WouoUI_ListPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_ListPageInParaInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>WouoUI_ListPageIndicatorCtrl</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_ListPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = WouoUI_ListPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1a8]"></a>WouoUI_ListPageInit</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, wououi_page.o(i.WouoUI_ListPageInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ListPageInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageInit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
</UL>

<P><STRONG><a name="[1e9]"></a>WouoUI_ListPageLastItem</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, wououi_page.o(i.WouoUI_ListPageLastItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_ListPageLastItem
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
</UL>

<P><STRONG><a name="[1ea]"></a>WouoUI_ListPageNextItem</STRONG> (Thumb, 178 bytes, Stack size 24 bytes, wououi_page.o(i.WouoUI_ListPageNextItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_ListPageNextItem
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
</UL>

<P><STRONG><a name="[a]"></a>WouoUI_ListPageReact</STRONG> (Thumb, 218 bytes, Stack size 32 bytes, wououi_page.o(i.WouoUI_ListPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ListPageReact &rArr; WouoUI_ListPageNextItem
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageNextItem
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageLastItem
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>WouoUI_ListPageScrollBarCtrl</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_ListPageScrollBarCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = WouoUI_ListPageScrollBarCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>WouoUI_ListPageShow</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, wououi_page.o(i.WouoUI_ListPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 340<LI>Call Chain = WouoUI_ListPageShow &rArr; WouoUI_ListDrawText_CheckBox &rArr; WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[183]"></a>WouoUI_ListTitlePageGetSelectOpt</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WouoUI_ListTitlePageGetSelectOpt &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RightWheelMenu_CallBack
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParamAdjust_CallBack
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MainMenu_CallBack
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeftWheelMenu_CallBack
</UL>

<P><STRONG><a name="[26]"></a>WouoUI_ListWinPageIn</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ListWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = WouoUI_ListWinPageIn &rArr; _WouoUI_listWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[27]"></a>WouoUI_ListWinPageInParaInit</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, wououi_win.o(i.WouoUI_ListWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_ListWinPageInParaInit &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[2a]"></a>WouoUI_ListWinPageIndicatorCtrl</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = WouoUI_ListWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1ec]"></a>WouoUI_ListWinPageLastItem</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_ListWinPageLastItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_ListWinPageLastItem
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
</UL>

<P><STRONG><a name="[1ed]"></a>WouoUI_ListWinPageNextItem</STRONG> (Thumb, 164 bytes, Stack size 20 bytes, wououi_win.o(i.WouoUI_ListWinPageNextItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = WouoUI_ListWinPageNextItem
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
</UL>

<P><STRONG><a name="[29]"></a>WouoUI_ListWinPageReact</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ListWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ListWinPageReact &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageNextItem
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageLastItem
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[2b]"></a>WouoUI_ListWinPageScrollBarCtrl</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = WouoUI_ListWinPageScrollBarCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[28]"></a>WouoUI_ListWinPageShow</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ListWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = WouoUI_ListWinPageShow &rArr; _WouoUI_listWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1ee]"></a>WouoUI_MsgQueIsEmpty</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, wououi_msg.o(i.WouoUI_MsgQueIsEmpty))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>

<P><STRONG><a name="[1f0]"></a>WouoUI_MsgQueIsFull</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, wououi_msg.o(i.WouoUI_MsgQueIsFull))
<BR><BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueSend
</UL>

<P><STRONG><a name="[1da]"></a>WouoUI_MsgQueRead</STRONG> (Thumb, 34 bytes, Stack size 4 bytes, wououi_msg.o(i.WouoUI_MsgQueRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = WouoUI_MsgQueRead
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueIsEmpty
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageReact
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageReact
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
</UL>

<P><STRONG><a name="[1ef]"></a>WouoUI_MsgQueSend</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, wououi_msg.o(i.WouoUI_MsgQueSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = WouoUI_MsgQueSend
</UL>
<BR>[Calls]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueIsFull
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
</UL>

<P><STRONG><a name="[12]"></a>WouoUI_MsgWinPageIn</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, wououi_win.o(i.WouoUI_MsgWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = WouoUI_MsgWinPageIn &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[13]"></a>WouoUI_MsgWinPageInParaInit</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_MsgWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_MsgWinPageInParaInit &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrHeightAutoNewLine
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[16]"></a>WouoUI_MsgWinPageIndicatorCtrl</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_MsgWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = WouoUI_MsgWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBoxEmpty &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>WouoUI_MsgWinPageReact</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_MsgWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = WouoUI_MsgWinPageReact &rArr; WouoUI_MsgQueRead
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageSlideUpTxt
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageSlideDownTxt
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[14]"></a>WouoUI_MsgWinPageShow</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_MsgWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = WouoUI_MsgWinPageShow &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1f2]"></a>WouoUI_MsgWinPageSlideDownTxt</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_MsgWinPageSlideDownTxt))
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageReact
</UL>

<P><STRONG><a name="[1f1]"></a>WouoUI_MsgWinPageSlideUpTxt</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_MsgWinPageSlideUpTxt))
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageReact
</UL>

<P><STRONG><a name="[1e8]"></a>WouoUI_PageInit</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, wououi_page.o(i.WouoUI_PageInit))
<BR><BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInit
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInit
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageInit
</UL>

<P><STRONG><a name="[1de]"></a>WouoUI_PageReturn</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, wououi_page.o(i.WouoUI_PageReturn))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageReact
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageReact
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
</UL>

<P><STRONG><a name="[1f3]"></a>WouoUI_Proc</STRONG> (Thumb, 390 bytes, Stack size 24 bytes, wououi.o(i.WouoUI_Proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = WouoUI_Proc &rArr; WouoUI_BlurProc &rArr; WouoUI_BuffAllBlur &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueIsEmpty
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffSend
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffClear
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ScrollBarProc
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_IndicatorProc
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BlurProc
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BlurParaInit
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[1f4]"></a>WouoUI_ScrollBarProc</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, wououi.o(i.WouoUI_ScrollBarProc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_ScrollBarProc &rArr; WouoUI_Animation
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>

<P><STRONG><a name="[1a3]"></a>WouoUI_SelectDefaultUI</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, wououi.o(i.WouoUI_SelectDefaultUI))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_SelectDefaultUI &rArr; WouoUI_SetCurrentUI
</UL>
<BR>[Calls]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SetCurrentUI
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f5]"></a>WouoUI_SetCurrentUI</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, wououi.o(i.WouoUI_SetCurrentUI))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_SetCurrentUI
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetSendBuffFun
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPen
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetBuff
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SelectDefaultUI
</UL>

<P><STRONG><a name="[1f9]"></a>WouoUI_SpinWinPageChangeSelbit</STRONG> (Thumb, 68 bytes, Stack size 12 bytes, wououi_win.o(i.WouoUI_SpinWinPageChangeSelbit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = WouoUI_SpinWinPageChangeSelbit
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
</UL>

<P><STRONG><a name="[21]"></a>WouoUI_SpinWinPageIn</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_SpinWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = WouoUI_SpinWinPageIn &rArr; _WouoUI_SpinWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[22]"></a>WouoUI_SpinWinPageInParaInit</STRONG> (Thumb, 294 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_SpinWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_SpinWinPageInParaInit &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[25]"></a>WouoUI_SpinWinPageIndicatorCtrl</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = WouoUI_SpinWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[24]"></a>WouoUI_SpinWinPageReact</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_SpinWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_SpinWinPageReact &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageToggleSelState
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShiftSelbit
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageChangeSelbit
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1fa]"></a>WouoUI_SpinWinPageShiftSelbit</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_SpinWinPageShiftSelbit))
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
</UL>

<P><STRONG><a name="[23]"></a>WouoUI_SpinWinPageShow</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_SpinWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 276<LI>Call Chain = WouoUI_SpinWinPageShow &rArr; _WouoUI_SpinWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1fb]"></a>WouoUI_SpinWinPageToggleSelState</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_SpinWinPageToggleSelState))
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
</UL>

<P><STRONG><a name="[1fc]"></a>WouoUI_TItlePageNextItem</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_TItlePageNextItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_TItlePageNextItem
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageReact
</UL>

<P><STRONG><a name="[1]"></a>WouoUI_TitlePageIn</STRONG> (Thumb, 262 bytes, Stack size 32 bytes, wououi_page.o(i.WouoUI_TitlePageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = WouoUI_TitlePageIn &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBMP
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>WouoUI_TitlePageInParaInit</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, wououi_page.o(i.WouoUI_TitlePageInParaInit))
<BR><BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>WouoUI_TitlePageIndicatorCtrl</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_TitlePageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = WouoUI_TitlePageIndicatorCtrl &rArr; WouoUI_CanvasDrawBoxRightAngle &rArr; WouoUI_CanvasDrawLine_V &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBoxRightAngle
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1a7]"></a>WouoUI_TitlePageInit</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, wououi_page.o(i.WouoUI_TitlePageInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_TitlePageInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInParaInit
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageInit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
</UL>

<P><STRONG><a name="[1fd]"></a>WouoUI_TitlePageLastItem</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_TitlePageLastItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_TitlePageLastItem
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageReact
</UL>

<P><STRONG><a name="[4]"></a>WouoUI_TitlePageReact</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_TitlePageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WouoUI_TitlePageReact &rArr; WouoUI_TitlePageLastItem
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageLastItem
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TItlePageNextItem
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>WouoUI_TitlePageShow</STRONG> (Thumb, 238 bytes, Stack size 40 bytes, wououi_page.o(i.WouoUI_TitlePageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = WouoUI_TitlePageShow &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBMP
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1c]"></a>WouoUI_ValWinPageIn</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ValWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = WouoUI_ValWinPageIn &rArr; _WouoUI_ValWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1d]"></a>WouoUI_ValWinPageInParaInit</STRONG> (Thumb, 262 bytes, Stack size 16 bytes, wououi_win.o(i.WouoUI_ValWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_ValWinPageInParaInit &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[20]"></a>WouoUI_ValWinPageIndicatorCtrl</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = WouoUI_ValWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1a9]"></a>WouoUI_ValWinPageInit</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, wououi_win.o(i.WouoUI_ValWinPageInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = WouoUI_ValWinPageInit &rArr; WouoUI_ValWinPageSetMinStepMax &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageSetMinStepMax
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageInit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
</UL>

<P><STRONG><a name="[1f]"></a>WouoUI_ValWinPageReact</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ValWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ValWinPageReact &rArr; _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageValIncrease
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageValDecrease
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[184]"></a>WouoUI_ValWinPageSetMinStepMax</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WouoUI_ValWinPageSetMinStepMax &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInit
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RightWheelMenu_CallBack
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeftWheelMenu_CallBack
</UL>

<P><STRONG><a name="[1e]"></a>WouoUI_ValWinPageShow</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, wououi_win.o(i.WouoUI_ValWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = WouoUI_ValWinPageShow &rArr; _WouoUI_ValWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[1ab]"></a>WouoUI_ValWinPageValDecrease</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_ValWinPageValDecrease))
<BR><BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParamAdjust_CallBack
</UL>

<P><STRONG><a name="[1aa]"></a>WouoUI_ValWinPageValIncrease</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, wououi_win.o(i.WouoUI_ValWinPageValIncrease))
<BR><BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParamAdjust_CallBack
</UL>

<P><STRONG><a name="[1ff]"></a>WouoUI_WavePageCanShiftWave</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, wououi_page.o(i.WouoUI_WavePageCanShiftWave))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WouoUI_WavePageCanShiftWave &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageRightShiftWave
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageLeftShiftWave
</UL>

<P><STRONG><a name="[e]"></a>WouoUI_WavePageInParaInit</STRONG> (Thumb, 112 bytes, Stack size 0 bytes, wououi_page.o(i.WouoUI_WavePageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_WavePageInParaInit &rArr; _WouoUI_WaveAnimParaReaset
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveAnimParaReaset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[11]"></a>WouoUI_WavePageIndicatorCtrl</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_WavePageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = WouoUI_WavePageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBoxEmpty &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[201]"></a>WouoUI_WavePageLeftShiftWave</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_WavePageLeftShiftWave))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_WavePageLeftShiftWave &rArr; WouoUI_WavePageCanShiftWave &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageCanShiftWave
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveUpdateRange
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
</UL>

<P><STRONG><a name="[10]"></a>WouoUI_WavePageReact</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_WavePageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = WouoUI_WavePageReact &rArr; WouoUI_WavePageRightShiftWave &rArr; WouoUI_WavePageCanShiftWave &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageStopRestartWave
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShowNextWaveData
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShowLastWaveData
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageRightShiftWave
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageLeftShiftWave
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[205]"></a>WouoUI_WavePageRightShiftWave</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, wououi_page.o(i.WouoUI_WavePageRightShiftWave))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_WavePageRightShiftWave &rArr; WouoUI_WavePageCanShiftWave &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageCanShiftWave
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveUpdateRange
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
</UL>

<P><STRONG><a name="[f]"></a>WouoUI_WavePageShow</STRONG> (Thumb, 998 bytes, Stack size 48 bytes, wououi_page.o(i.WouoUI_WavePageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = WouoUI_WavePageShow &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawPoint
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data)
</UL>
<P><STRONG><a name="[203]"></a>WouoUI_WavePageShowLastWaveData</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, wououi_page.o(i.WouoUI_WavePageShowLastWaveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_WavePageShowLastWaveData &rArr; _WouoUI_WaveAnimParaReaset
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveAnimParaReaset
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
</UL>

<P><STRONG><a name="[204]"></a>WouoUI_WavePageShowNextWaveData</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, wououi_page.o(i.WouoUI_WavePageShowNextWaveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_WavePageShowNextWaveData &rArr; _WouoUI_WaveAnimParaReaset
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveAnimParaReaset
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
</UL>

<P><STRONG><a name="[206]"></a>WouoUI_WavePageStopRestartWave</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, wououi_page.o(i.WouoUI_WavePageStopRestartWave))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_WavePageStopRestartWave &rArr; _WouoUI_WaveUpdateRange
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveUpdateRange
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
</UL>

<P><STRONG><a name="[20c]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[32b]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1e2]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInParaInit
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageInParaInit
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_JumpToPage
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WinGetBGSelectItem
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageSetMinStepMax
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInit
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageStopRestartWave
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageCanShiftWave
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInit
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListTitlePageGetSelectOpt
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageInit
<LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_print
</UL>

<P><STRONG><a name="[32c]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[32d]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[20e]"></a>__0snprintf</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[32e]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[239]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
</UL>

<P><STRONG><a name="[32f]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[330]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[20f]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[331]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[209]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>

<P><STRONG><a name="[332]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[333]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[210]"></a>__0vsnprintf</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[334]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[335]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[336]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[2e0]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
</UL>

<P><STRONG><a name="[211]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[113]"></a>__hardfp_atan2f</STRONG> (Thumb, 502 bytes, Stack size 16 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Component_Phase
</UL>

<P><STRONG><a name="[214]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sine
</UL>

<P><STRONG><a name="[118]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Vpp
</UL>

<P><STRONG><a name="[218]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[213]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[217]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[212]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[215]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[337]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[338]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[339]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[216]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[2d]"></a>adc_task</STRONG> (Thumb, 362 bytes, Stack size 88 bytes, adc_app.o(i.adc_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 4540<LI>Call Chain = adc_task &rArr; Get_Waveform_Info &rArr; Analyze_Harmonics &rArr; arm_cfft_radix4_f32 &rArr; arm_radix4_butterfly_inverse_f32 &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_get_update_frequency
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Info
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetWaveformTypeString
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[223]"></a>adc_tim_dma_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, adc_app.o(i.adc_tim_dma_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = adc_tim_dma_init &rArr; HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[225]"></a>app_btn_init</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, btn_app.o(i.app_btn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = app_btn_init &rArr; ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2e]"></a>btn_task</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, btn_app.o(i.btn_task))
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[fa]"></a>dac_app_get_adc_sampling_interval_us</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, dac_app.o(i.dac_app_get_adc_sampling_interval_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = dac_app_get_adc_sampling_interval_us &rArr; __aeabi_ul2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2f
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Waveform_Phase
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Harmonics
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Frequency_And_Type
</UL>

<P><STRONG><a name="[222]"></a>dac_app_get_update_frequency</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, dac_app.o(i.dac_app_get_update_frequency))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[21e]"></a>dac_app_set_amplitude</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, dac_app.o(i.dac_app_set_amplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = dac_app_set_amplitude &rArr; start_dac_dma &rArr; update_timer_frequency &rArr; update_adc_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_dac_dma
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[281]"></a>disk_initialize</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[2f7]"></a>disk_ioctl</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[275]"></a>disk_read</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[280]"></a>disk_status</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[279]"></a>disk_write</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[226]"></a>ebtn_init</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[16c]"></a>ebtn_process</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[258]"></a>ebtn_process_with_curr_state</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_process_with_curr_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[25e]"></a>f_close</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = f_close &rArr; f_sync &rArr; sync_fs &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dec_lock
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[262]"></a>f_closedir</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, ff.o(i.f_closedir))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = f_closedir &rArr; validate
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dec_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[263]"></a>f_mkdir</STRONG> (Thumb, 380 bytes, Stack size 104 bytes, ff.o(i.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_mkdir &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[26d]"></a>f_mount</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, ff.o(i.f_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = f_mount &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[270]"></a>f_open</STRONG> (Thumb, 566 bytes, Stack size 104 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inc_lock
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enq_lock
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_lock
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[276]"></a>f_opendir</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, ff.o(i.f_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inc_lock
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[277]"></a>f_read</STRONG> (Thumb, 392 bytes, Stack size 40 bytes, ff.o(i.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[27a]"></a>f_readdir</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, ff.o(i.f_readdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = f_readdir &rArr; dir_read &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[25f]"></a>f_sync</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = f_sync &rArr; sync_fs &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[27d]"></a>f_write</STRONG> (Thumb, 452 bytes, Stack size 40 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
</UL>

<P><STRONG><a name="[243]"></a>ff_convert</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, cc936.o(i.ff_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[265]"></a>ff_memalloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(i.ff_memalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ff_memalloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[26b]"></a>ff_memfree</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(i.ff_memfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_memfree &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[23e]"></a>ff_wtoupper</STRONG> (Thumb, 156 bytes, Stack size 4 bytes, cc936.o(i.ff_wtoupper))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[a9]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = fputc &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[27f]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
</UL>

<P><STRONG><a name="[267]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fatfs.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[285]"></a>led_disp</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, led_app.o(i.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[2c]"></a>led_task</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, led_app.o(i.led_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = led_task &rArr; led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[289]"></a>lfs_basic_test</STRONG> (Thumb, 384 bytes, Stack size 112 bytes, flash_app.o(i.lfs_basic_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 1240<LI>Call Chain = lfs_basic_test &rArr; lfs_mkdir &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_rewind
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2b9]"></a>lfs_crc</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, lfs_util.o(i.lfs_crc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_crc
</UL>
<BR>[Called By]<UL><LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
</UL>

<P><STRONG><a name="[236]"></a>lfs_dir_close</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, lfs.o(i.lfs_dir_close))
<BR><BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_rm
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>

<P><STRONG><a name="[233]"></a>lfs_dir_open</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = lfs_dir_open &rArr; lfs_dir_find &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_rm
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>

<P><STRONG><a name="[234]"></a>lfs_dir_read</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lfs_dir_read &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getinfo
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>

<P><STRONG><a name="[232]"></a>lfs_file_close</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, lfs.o(i.lfs_file_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = lfs_file_close &rArr; lfs_file_sync &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_write
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_touch
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cat
</UL>

<P><STRONG><a name="[230]"></a>lfs_file_open</STRONG> (Thumb, 12 bytes, Stack size 16 bytes, lfs.o(i.lfs_file_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 1048<LI>Call Chain = lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_write
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_touch
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cat
</UL>

<P><STRONG><a name="[2c9]"></a>lfs_file_opencfg</STRONG> (Thumb, 552 bytes, Stack size 88 bytes, lfs.o(i.lfs_file_opencfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 1032<LI>Call Chain = lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_malloc
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
</UL>

<P><STRONG><a name="[231]"></a>lfs_file_read</STRONG> (Thumb, 262 bytes, Stack size 56 bytes, lfs.o(i.lfs_file_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + In Cycle
<LI>Call Chain = lfs_file_read &rArr;  lfs_file_flush (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cat
</UL>

<P><STRONG><a name="[28c]"></a>lfs_file_rewind</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lfs.o(i.lfs_file_rewind))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = lfs_file_rewind &rArr; lfs_file_seek &rArr; lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_seek
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[2cc]"></a>lfs_file_seek</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, lfs.o(i.lfs_file_seek))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = lfs_file_seek &rArr; lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_rewind
</UL>

<P><STRONG><a name="[2c7]"></a>lfs_file_sync</STRONG> (Thumb, 202 bytes, Stack size 48 bytes, lfs.o(i.lfs_file_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 816<LI>Call Chain = lfs_file_sync &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
</UL>

<P><STRONG><a name="[237]"></a>lfs_file_write</STRONG> (Thumb, 428 bytes, Stack size 64 bytes, lfs.o(i.lfs_file_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + In Cycle
<LI>Call Chain = lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_write
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cp
</UL>

<P><STRONG><a name="[28b]"></a>lfs_format</STRONG> (Thumb, 230 bytes, Stack size 104 bytes, lfs.o(i.lfs_format))
<BR><BR>[Stack]<UL><LI>Max Depth = 872<LI>Call Chain = lfs_format &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[2bb]"></a>lfs_fs_size</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_fs_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_fs_size &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[288]"></a>lfs_fs_traverse</STRONG> (Thumb, 256 bytes, Stack size 80 bytes, lfs.o(i.lfs_fs_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_size
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[23a]"></a>lfs_mkdir</STRONG> (Thumb, 326 bytes, Stack size 184 bytes, lfs.o(i.lfs_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = lfs_mkdir &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_mkdir
</UL>

<P><STRONG><a name="[28a]"></a>lfs_mount</STRONG> (Thumb, 414 bytes, Stack size 112 bytes, lfs.o(i.lfs_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_mount &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmove
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[23c]"></a>lfs_remove</STRONG> (Thumb, 202 bytes, Stack size 120 bytes, lfs.o(i.lfs_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 1064<LI>Call Chain = lfs_remove &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_rm
</UL>

<P><STRONG><a name="[23b]"></a>lfs_rename</STRONG> (Thumb, 454 bytes, Stack size 208 bytes, lfs.o(i.lfs_rename))
<BR><BR>[Stack]<UL><LI>Max Depth = 1152<LI>Call Chain = lfs_rename &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_mv
</UL>

<P><STRONG><a name="[2da]"></a>lfs_storage_init</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, lfs_port.o(i.lfs_storage_init))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[28d]"></a>list_dir_recursive</STRONG> (Thumb, 178 bytes, Stack size 464 bytes, flash_app.o(i.list_dir_recursive))
<BR><BR>[Stack]<UL><LI>Max Depth = 1072 + In Cycle
<LI>Call Chain = list_dir_recursive &rArr;  list_dir_recursive (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_close
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>

<P><STRONG><a name="[95]"></a>main</STRONG> (Thumb, 198 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1320<LI>Call Chain = main &rArr; test_sd_fatfs &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetPowerSave
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_InitDisplay
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_set_uart
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_init
<LI><a href="#[2df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_storage_init
<LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SelectDefaultUI
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_AttachSendBuffFun
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PIDMenu_Init
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_FFT_Init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SDIO_SD_Init
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[27e]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_malloc
</UL>

<P><STRONG><a name="[21f]"></a>my_printf</STRONG> (Thumb, 50 bytes, Stack size 544 bytes, usart_app.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_sd_fatfs
<LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
<LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_print
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;list_dir_recursive
</UL>

<P><STRONG><a name="[30]"></a>oled_task</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, oled_app.o(i.oled_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = oled_task &rArr; WouoUI_Proc &rArr; WouoUI_BlurProc &rArr; WouoUI_BuffAllBlur &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Proc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[ac]"></a>prv_btn_event</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, btn_app.o(i.prv_btn_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = prv_btn_event &rArr; WouoUI_MsgQueSend
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueSend
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(i.app_btn_init)
</UL>
<P><STRONG><a name="[ad]"></a>prv_btn_get_state</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, btn_app.o(i.prv_btn_get_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prv_btn_get_state
</UL>
<BR>[Calls]<UL><LI><a href="#[2e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(i.app_btn_init)
</UL>
<P><STRONG><a name="[2e2]"></a>rt_ringbuffer_data_len</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ringbuffer.o(i.rt_ringbuffer_data_len))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_ringbuffer_data_len
</UL>
<BR>[Calls]<UL><LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_status
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[2e4]"></a>rt_ringbuffer_get</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_ringbuffer_get &rArr; rt_ringbuffer_data_len
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[2d5]"></a>rt_ringbuffer_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ringbuffer.o(i.rt_ringbuffer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_ringbuffer_init
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[171]"></a>rt_ringbuffer_put</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_ringbuffer_put &rArr; rt_ringbuffer_data_len
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[2dd]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2df]"></a>scheduler_run</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2e6]"></a>shell_execute</STRONG> (Thumb, 120 bytes, Stack size 120 bytes, shell_app.o(i.shell_execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 880<LI>Call Chain = shell_execute &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process_char
</UL>

<P><STRONG><a name="[2db]"></a>shell_init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, shell_app.o(i.shell_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 768<LI>Call Chain = shell_init &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2ee]"></a>shell_print</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, shell_app.o(i.shell_print))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
</UL>

<P><STRONG><a name="[22e]"></a>shell_printf</STRONG> (Thumb, 30 bytes, Stack size 152 bytes, shell_app.o(i.shell_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_print
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_init
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_execute
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process_char
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_history
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_write
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_touch
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_rm
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_pwd
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_mv
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_mkdir
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_help
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_echo
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cp
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_clear
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cat
</UL>

<P><STRONG><a name="[2ef]"></a>shell_process</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, shell_app.o(i.shell_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 10048<LI>Call Chain = shell_process &rArr; shell_process_char &rArr; shell_handle_tab &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process_char
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[2dc]"></a>shell_set_uart</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, shell_app.o(i.shell_set_uart))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2a1]"></a>spi_flash_buffer_read</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deskio_read
</UL>

<P><STRONG><a name="[2a0]"></a>spi_flash_buffer_write</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, gd25qxx.o(i.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deskio_prog
</UL>

<P><STRONG><a name="[2d9]"></a>spi_flash_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd25qxx.o(i.spi_flash_init))
<BR><BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2f2]"></a>spi_flash_page_write</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[2f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[29f]"></a>spi_flash_sector_erase</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[2f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deskio_erase
</UL>

<P><STRONG><a name="[2f1]"></a>spi_flash_send_byte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[2f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>

<P><STRONG><a name="[2f4]"></a>spi_flash_wait_for_write_end</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_wait_for_write_end))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[2f3]"></a>spi_flash_write_enable</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_write_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[f4]"></a>sqrtf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, sqrtf.o(i.sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
</UL>

<P><STRONG><a name="[2de]"></a>test_sd_fatfs</STRONG> (Thumb, 408 bytes, Stack size 712 bytes, flash_app.o(i.test_sd_fatfs))
<BR><BR>[Stack]<UL><LI>Max Depth = 1320<LI>Call Chain = test_sd_fatfs &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2f8]"></a>u8g2_IsIntersection</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, u8g2_intersection.o(i.u8g2_IsIntersection))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_IsIntersection
</UL>
<BR>[Calls]<UL><LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_is_intersection_decision_tree
</UL>
<BR>[Called By]<UL><LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>

<P><STRONG><a name="[1a1]"></a>u8g2_SendBuffer</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, u8g2_buffer.o(i.u8g2_SendBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_SendBuffer &rArr; u8g2_send_buffer &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[2fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_RefreshDisplay
<LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SendBuff
</UL>

<P><STRONG><a name="[2fe]"></a>u8g2_SetFontPosBaseline</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, u8g2_font.o(i.u8g2_SetFontPosBaseline))
<BR><BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[2fd]"></a>u8g2_SetMaxClipWindow</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, u8g2_setup.o(i.u8g2_SetMaxClipWindow))
<BR><BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[2fc]"></a>u8g2_SetupBuffer</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, u8g2_setup.o(i.u8g2_SetupBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_SetupBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[2fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontPosBaseline
<LI><a href="#[2fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetMaxClipWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
</UL>

<P><STRONG><a name="[2d6]"></a>u8g2_Setup_ssd1306_i2c_128x32_univision_f</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_Setup_ssd1306_i2c_128x32_univision_f &rArr; u8x8_Setup &rArr; u8x8_SetupMemory
</UL>
<BR>[Calls]<UL><LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
<LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_m_16_4_f
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[302]"></a>u8g2_draw_hv_line_2dir</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8g2_hvline.o(i.u8g2_draw_hv_line_2dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_draw_hv_line_2dir
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r0
</UL>

<P><STRONG><a name="[c8]"></a>u8g2_draw_l90_r0</STRONG> (Thumb, 12 bytes, Stack size 16 bytes, u8g2_setup.o(i.u8g2_draw_l90_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_draw_l90_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[302]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[bc]"></a>u8g2_font_calc_vref_font</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, u8g2_font.o(i.u8g2_font_calc_vref_font))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(i.u8g2_SetFontPosBaseline)
</UL>
<P><STRONG><a name="[b9]"></a>u8g2_gpio_and_delay_stm32</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, oled_app.o(i.u8g2_gpio_and_delay_stm32))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8g2_gpio_and_delay_stm32 &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[2f9]"></a>u8g2_is_intersection_decision_tree</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, u8g2_intersection.o(i.u8g2_is_intersection_decision_tree))
<BR><BR>[Called By]<UL><LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[bf]"></a>u8g2_ll_hvline_vertical_top_lsb</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = u8g2_ll_hvline_vertical_top_lsb
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
</UL>
<P><STRONG><a name="[300]"></a>u8g2_m_16_4_f</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_d_memory.o(i.u8g2_m_16_4_f))
<BR><BR>[Called By]<UL><LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
</UL>

<P><STRONG><a name="[c6]"></a>u8g2_update_dimension_r0</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, u8g2_setup.o(i.u8g2_update_dimension_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = u8g2_update_dimension_r0 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[c7]"></a>u8g2_update_page_win_r0</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, u8g2_setup.o(i.u8g2_update_page_win_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_update_page_win_r0 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection
</UL>
<BR>[Calls]<UL><LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[303]"></a>u8x8_DrawTile</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_DrawTile))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_DrawTile
</UL>
<BR>[Called By]<UL><LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>

<P><STRONG><a name="[2d7]"></a>u8x8_InitDisplay</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_display.o(i.u8x8_InitDisplay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_InitDisplay
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2fb]"></a>u8x8_RefreshDisplay</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_display.o(i.u8x8_RefreshDisplay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_RefreshDisplay
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
</UL>

<P><STRONG><a name="[2d8]"></a>u8x8_SetPowerSave</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_display.o(i.u8x8_SetPowerSave))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_SetPowerSave
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2ff]"></a>u8x8_Setup</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_setup.o(i.u8x8_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8x8_Setup &rArr; u8x8_SetupMemory
</UL>
<BR>[Calls]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetupMemory
<LI><a href="#[305]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetupDefaults
</UL>
<BR>[Called By]<UL><LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
</UL>

<P><STRONG><a name="[305]"></a>u8x8_SetupDefaults</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, u8x8_setup.o(i.u8x8_SetupDefaults))
<BR><BR>[Called By]<UL><LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
</UL>

<P><STRONG><a name="[306]"></a>u8x8_SetupMemory</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_display.o(i.u8x8_SetupMemory))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_SetupMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
</UL>

<P><STRONG><a name="[30a]"></a>u8x8_byte_EndTransfer</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_byte.o(i.u8x8_byte_EndTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_byte_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[30c]"></a>u8x8_byte_SendByte</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, u8x8_byte.o(i.u8x8_byte_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_byte_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[30b]"></a>u8x8_byte_StartTransfer</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_byte.o(i.u8x8_byte_StartTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_byte_StartTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[ba]"></a>u8x8_byte_hw_i2c</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, oled_app.o(i.u8x8_byte_hw_i2c))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = u8x8_byte_hw_i2c &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[313]"></a>u8x8_cad_EndTransfer</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_cad.o(i.u8x8_cad_EndTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_cad_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[312]"></a>u8x8_cad_SendArg</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_cad.o(i.u8x8_cad_SendArg))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_cad_SendArg
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[311]"></a>u8x8_cad_SendCmd</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_cad.o(i.u8x8_cad_SendCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_cad_SendCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[308]"></a>u8x8_cad_SendData</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_cad.o(i.u8x8_cad_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_cad_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
<LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[307]"></a>u8x8_cad_SendSequence</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, u8x8_cad.o(i.u8x8_cad_SendSequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[310]"></a>u8x8_cad_StartTransfer</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_cad.o(i.u8x8_cad_StartTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_cad_StartTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[bd]"></a>u8x8_cad_ssd13xx_fast_i2c</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_cad_ssd13xx_fast_i2c &rArr; u8x8_i2c_data_transfer &rArr; u8x8_byte_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[30a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
</UL>
<P><STRONG><a name="[30e]"></a>u8x8_d_helper_display_init</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_d_helper_display_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_d_helper_display_init &rArr; u8x8_gpio_call
</UL>
<BR>[Calls]<UL><LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[314]"></a>u8x8_d_helper_display_setup_memory</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, u8x8_display.o(i.u8x8_d_helper_display_setup_memory))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_univision
</UL>

<P><STRONG><a name="[be]"></a>u8x8_d_ssd1306_128x32_univision</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_univision))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_d_ssd1306_128x32_univision &rArr; u8x8_d_ssd1306_128x32_generic &rArr; u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
</UL>
<P><STRONG><a name="[c0]"></a>u8x8_dummy_cb</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, u8x8_setup.o(i.u8x8_dummy_cb))
<BR>[Address Reference Count : 1]<UL><LI> u8x8_setup.o(i.u8x8_SetupDefaults)
</UL>
<P><STRONG><a name="[309]"></a>u8x8_gpio_call</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, u8x8_gpio.o(i.u8x8_gpio_call))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[30e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[2f]"></a>uart_task</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, usart_app.o(i.uart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 10056<LI>Call Chain = uart_task &rArr; shell_process &rArr; shell_process_char &rArr; shell_handle_tab &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[315]"></a>ui_ftoa_f</STRONG> (Thumb, 162 bytes, Stack size 16 bytes, wououi.o(i.ui_ftoa_f))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ui_ftoa_f &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f_str
</UL>

<P><STRONG><a name="[1e7]"></a>ui_ftoa_f_str</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, wououi.o(i.ui_ftoa_f_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ui_ftoa_f_str &rArr; ui_ftoa_f &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[207]"></a>ui_ftoa_g</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, wououi.o(i.ui_ftoa_g))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ui_ftoa_g &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
</UL>

<P><STRONG><a name="[208]"></a>ui_ftoa_g_str</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, wououi.o(i.ui_ftoa_g_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ui_ftoa_g_str &rArr; ui_ftoa_g &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>

<P><STRONG><a name="[1e6]"></a>ui_itoa_str</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, wououi.o(i.ui_itoa_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ui_itoa_str &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[1ba]"></a>SD_CheckStatus</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, sd_diskio.o(i.SD_CheckStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = SD_CheckStatus &rArr; BSP_SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_status
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[22a]"></a>check_fs</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[244]"></a>chk_chr</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[271]"></a>chk_lock</STRONG> (Thumb, 108 bytes, Stack size 12 bytes, ff.o(i.chk_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = chk_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[26f]"></a>clear_lock</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, ff.o(i.clear_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = clear_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[278]"></a>clmt_clust</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, ff.o(i.clmt_clust))
<BR><BR>[Called By]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[250]"></a>clust2sect</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[23d]"></a>cmp_lfn</STRONG> (Thumb, 148 bytes, Stack size 40 bytes, ff.o(i.cmp_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = cmp_lfn &rArr; ff_wtoupper
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[23f]"></a>create_chain</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[242]"></a>create_name</STRONG> (Thumb, 578 bytes, Stack size 48 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = create_name &rArr; ff_convert
</UL>
<BR>[Calls]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[261]"></a>dec_lock</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, ff.o(i.dec_lock))
<BR><BR>[Called By]<UL><LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[249]"></a>dir_alloc</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, ff.o(i.dir_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dir_alloc &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[24c]"></a>dir_find</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[24b]"></a>dir_next</STRONG> (Thumb, 294 bytes, Stack size 32 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[251]"></a>dir_read</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, ff.o(i.dir_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dir_read &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pick_lfn
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>
<BR>[Called By]<UL><LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[253]"></a>dir_register</STRONG> (Thumb, 310 bytes, Stack size 40 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_lfn
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[24a]"></a>dir_sdi</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[272]"></a>enq_lock</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, ff.o(i.enq_lock))
<BR><BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[264]"></a>find_volume</STRONG> (Thumb, 714 bytes, Stack size 40 bytes, ff.o(i.find_volume))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_lock
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[266]"></a>follow_path</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[255]"></a>gen_numname</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, ff.o(i.gen_numname))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gen_numname
</UL>
<BR>[Calls]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[240]"></a>get_fat</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[27b]"></a>get_fileinfo</STRONG> (Thumb, 272 bytes, Stack size 32 bytes, ff.o(i.get_fileinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_fileinfo &rArr; ff_convert
</UL>
<BR>[Calls]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[26e]"></a>get_ldnumber</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, ff.o(i.get_ldnumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = get_ldnumber
</UL>
<BR>[Called By]<UL><LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[274]"></a>inc_lock</STRONG> (Thumb, 144 bytes, Stack size 12 bytes, ff.o(i.inc_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = inc_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[273]"></a>ld_clust</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ff.o(i.ld_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ld_clust
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[22d]"></a>ld_dword</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ff.o(i.ld_dword))
<BR><BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[22c]"></a>ld_word</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ff.o(i.ld_word))
<BR><BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pick_lfn
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[24e]"></a>mem_cmp</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ff.o(i.mem_cmp))
<BR><BR>[Calls]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[254]"></a>mem_cpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ff.o(i.mem_cpy))
<BR><BR>[Called By]<UL><LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[245]"></a>mem_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ff.o(i.mem_set))
<BR><BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[22b]"></a>move_window</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_alloc
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[252]"></a>pick_lfn</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, ff.o(i.pick_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = pick_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
</UL>

<P><STRONG><a name="[241]"></a>put_fat</STRONG> (Thumb, 258 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[256]"></a>put_lfn</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, ff.o(i.put_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = put_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
</UL>
<BR>[Called By]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[26a]"></a>remove_chain</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[269]"></a>st_clust</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ff.o(i.st_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = st_clust
</UL>
<BR>[Calls]<UL><LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
</UL>
<BR>[Called By]<UL><LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[268]"></a>st_dword</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(i.st_dword))
<BR><BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[27c]"></a>st_word</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ff.o(i.st_word))
<BR><BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_lfn
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[24d]"></a>sum_sfn</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.sum_sfn))
<BR><BR>[Called By]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[26c]"></a>sync_fs</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, ff.o(i.sync_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sync_fs &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
</UL>
<BR>[Called By]<UL><LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[24f]"></a>sync_window</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, ff.o(i.sync_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[260]"></a>validate</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>

<P><STRONG><a name="[98]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ADC_DMAConvCplt &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[9a]"></a>ADC_DMAError</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[99]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAHalfConvCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[11e]"></a>ADC_Init</STRONG> (Thumb, 284 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[12e]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[12d]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[12f]"></a>DMA_SetConfig</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[140]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[182]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[136]"></a>I2C_MasterRequestWrite</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[13a]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[138]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[135]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
</UL>

<P><STRONG><a name="[181]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
</UL>

<P><STRONG><a name="[137]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[145]"></a>SD_FindSCR</STRONG> (Thumb, 224 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(i.SD_FindSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[14f]"></a>SD_InitCard</STRONG> (Thumb, 238 bytes, Stack size 72 bytes, stm32f4xx_hal_sd.o(i.SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardCSD
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetPowerState
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[14e]"></a>SD_PowerON</STRONG> (Thumb, 174 bytes, Stack size 40 bytes, stm32f4xx_hal_sd.o(i.SD_PowerON))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = SD_PowerON &rArr; SDMMC_CmdAppCommand &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[15b]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[1bd]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>

<P><STRONG><a name="[163]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[161]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[162]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[a1]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[a8]"></a>UART_DMAError</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[a6]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[a7]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[173]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[172]"></a>UART_EndTxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[175]"></a>UART_Receive_IT</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[17b]"></a>UART_SetConfig</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[180]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[25d]"></a>bit_array_and</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[257]"></a>bit_array_assign</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, ebtn.o(i.bit_array_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[228]"></a>bit_array_cmp</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ebtn.o(i.bit_array_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[25a]"></a>bit_array_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ebtn.o(i.bit_array_get))
<BR><BR>[Called By]<UL><LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[259]"></a>ebtn_process_btn</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, ebtn.o(i.ebtn_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ebtn_process_btn &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
</UL>
<BR>[Called By]<UL><LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[25c]"></a>ebtn_process_btn_combo</STRONG> (Thumb, 172 bytes, Stack size 40 bytes, ebtn.o(i.ebtn_process_btn_combo))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[25b]"></a>prv_process_btn</STRONG> (Thumb, 328 bytes, Stack size 24 bytes, ebtn.o(i.prv_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prv_process_btn
</UL>
<BR>[Called By]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[2fa]"></a>u8g2_send_buffer</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, u8g2_buffer.o(i.u8g2_send_buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8g2_send_buffer &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[303]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
</UL>

<P><STRONG><a name="[301]"></a>u8g2_apply_clip_window</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, u8g2_setup.o(i.u8g2_apply_clip_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_apply_clip_window &rArr; u8g2_IsIntersection
</UL>
<BR>[Calls]<UL><LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r0
</UL>

<P><STRONG><a name="[304]"></a>u8g2_update_dimension_common</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, u8g2_setup.o(i.u8g2_update_dimension_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = u8g2_update_dimension_common
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r0
</UL>

<P><STRONG><a name="[30d]"></a>u8x8_i2c_data_transfer</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, u8x8_cad.o(i.u8x8_i2c_data_transfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_i2c_data_transfer &rArr; u8x8_byte_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[30a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
</UL>

<P><STRONG><a name="[30f]"></a>u8x8_d_ssd1306_128x32_generic</STRONG> (Thumb, 208 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8x8_d_ssd1306_128x32_generic &rArr; u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[30e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[310]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_StartTransfer
<LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
<LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[311]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendCmd
<LI><a href="#[312]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendArg
<LI><a href="#[313]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_univision
</UL>

<P><STRONG><a name="[2e3]"></a>rt_ringbuffer_status</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, ringbuffer.o(i.rt_ringbuffer_status))
<BR><BR>[Called By]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>

<P><STRONG><a name="[291]"></a>lfs_alignup</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lfs.o(i.lfs_alignup))
<BR><BR>[Called By]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[286]"></a>lfs_alloc</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, lfs.o(i.lfs_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[ae]"></a>lfs_alloc_lookahead</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, lfs.o(i.lfs_alloc_lookahead))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_alloc_lookahead
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_alloc)
</UL>
<P><STRONG><a name="[28e]"></a>lfs_bd_cmp</STRONG> (Thumb, 102 bytes, Stack size 72 bytes, lfs.o(i.lfs_bd_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find_match
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[295]"></a>lfs_bd_erase</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_bd_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_bd_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[290]"></a>lfs_bd_flush</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, lfs.o(i.lfs_bd_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
</UL>
<BR>[Called By]<UL><LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>

<P><STRONG><a name="[293]"></a>lfs_bd_prog</STRONG> (Thumb, 178 bytes, Stack size 56 bytes, lfs.o(i.lfs_bd_prog))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[28f]"></a>lfs_bd_read</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, lfs.o(i.lfs_bd_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
</UL>

<P><STRONG><a name="[292]"></a>lfs_cache_zero</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_cache_zero))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_cache_zero
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[297]"></a>lfs_ctz</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lfs.o(i.lfs_ctz))
<BR><BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[294]"></a>lfs_ctz_extend</STRONG> (Thumb, 354 bytes, Stack size 80 bytes, lfs.o(i.lfs_ctz_extend))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = lfs_ctz_extend &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
</UL>

<P><STRONG><a name="[29a]"></a>lfs_ctz_find</STRONG> (Thumb, 166 bytes, Stack size 72 bytes, lfs.o(i.lfs_ctz_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lfs_ctz_find &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
</UL>

<P><STRONG><a name="[29b]"></a>lfs_ctz_fromle32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_ctz_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_ctz_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getinfo
</UL>

<P><STRONG><a name="[296]"></a>lfs_ctz_index</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, lfs.o(i.lfs_ctz_index))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lfs_ctz_index
</UL>
<BR>[Calls]<UL><LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_popc
</UL>
<BR>[Called By]<UL><LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[29d]"></a>lfs_ctz_traverse</STRONG> (Thumb, 156 bytes, Stack size 80 bytes, lfs.o(i.lfs_ctz_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = lfs_ctz_traverse &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
</UL>

<P><STRONG><a name="[29e]"></a>lfs_deinit</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lfs.o(i.lfs_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_deinit &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
</UL>

<P><STRONG><a name="[2a2]"></a>lfs_dir_alloc</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = lfs_dir_alloc &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
</UL>

<P><STRONG><a name="[2a3]"></a>lfs_dir_commit</STRONG> (Thumb, 782 bytes, Stack size 168 bytes, lfs.o(i.lfs_dir_commit))
<BR><BR>[Stack]<UL><LI>Max Depth = 768 + In Cycle
<LI>Call Chain = lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_iszero
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
</UL>

<P><STRONG><a name="[af]"></a>lfs_dir_commit_commit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, lfs.o(i.lfs_dir_commit_commit))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = lfs_dir_commit_commit &rArr; lfs_dir_commitattr &rArr; lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>
<BR>[Address Reference Count : 2]<UL><LI> lfs.o(i.lfs_dir_commit)
<LI> lfs.o(i.lfs_dir_compact)
</UL>
<P><STRONG><a name="[b0]"></a>lfs_dir_commit_size</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, lfs.o(i.lfs_dir_commit_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_dir_commit_size &rArr; lfs_tag_dsize
</UL>
<BR>[Calls]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_compact)
</UL>
<P><STRONG><a name="[2b1]"></a>lfs_dir_commitattr</STRONG> (Thumb, 170 bytes, Stack size 56 bytes, lfs.o(i.lfs_dir_commitattr))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_dir_commitattr &rArr; lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit_commit
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2b3]"></a>lfs_dir_commitcrc</STRONG> (Thumb, 388 bytes, Stack size 64 bytes, lfs.o(i.lfs_dir_commitcrc))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lfs_dir_commitcrc &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2b8]"></a>lfs_dir_commitprog</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_commitprog))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>

<P><STRONG><a name="[2b4]"></a>lfs_dir_compact</STRONG> (Thumb, 818 bytes, Stack size 152 bytes, lfs.o(i.lfs_dir_compact))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_size
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_iszero
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2ab]"></a>lfs_dir_drop</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_drop))
<BR><BR>[Stack]<UL><LI>Max Depth = 800<LI>Call Chain = lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2b5]"></a>lfs_dir_fetch</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2be]"></a>lfs_dir_fetchmatch</STRONG> (Thumb, 936 bytes, Stack size 144 bytes, lfs.o(i.lfs_dir_fetchmatch))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>

<P><STRONG><a name="[2bf]"></a>lfs_dir_find</STRONG> (Thumb, 318 bytes, Stack size 88 bytes, lfs.o(i.lfs_dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lfs_dir_find &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcspn
<LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strspn
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
</UL>

<P><STRONG><a name="[b1]"></a>lfs_dir_find_match</STRONG> (Thumb, 82 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_find_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_dir_find_match &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
</UL>
<BR>[Address Reference Count : 2]<UL><LI> lfs.o(i.lfs_dir_find)
<LI> lfs.o(i.lfs_mount)
</UL>
<P><STRONG><a name="[2c2]"></a>lfs_dir_get</STRONG> (Thumb, 20 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getinfo
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[2af]"></a>lfs_dir_getgstate</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_getgstate))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_dir_getgstate &rArr; lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2c4]"></a>lfs_dir_getinfo</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_getinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = lfs_dir_getinfo &rArr; lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
</UL>

<P><STRONG><a name="[2c5]"></a>lfs_dir_getread</STRONG> (Thumb, 230 bytes, Stack size 64 bytes, lfs.o(i.lfs_dir_getread))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = lfs_dir_getread &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
</UL>

<P><STRONG><a name="[2c3]"></a>lfs_dir_getslice</STRONG> (Thumb, 280 bytes, Stack size 56 bytes, lfs.o(i.lfs_dir_getslice))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
</UL>

<P><STRONG><a name="[2ba]"></a>lfs_dir_split</STRONG> (Thumb, 112 bytes, Stack size 80 bytes, lfs.o(i.lfs_dir_split))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + In Cycle
<LI>Call Chain = lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[2ad]"></a>lfs_dir_traverse</STRONG> (Thumb, 436 bytes, Stack size 104 bytes, lfs.o(i.lfs_dir_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + In Cycle
<LI>Call Chain = lfs_dir_traverse &rArr;  lfs_dir_traverse (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[b2]"></a>lfs_dir_traverse_filter</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, lfs.o(i.lfs_dir_traverse_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_dir_traverse_filter
</UL>
<BR>[Calls]<UL><LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_traverse)
</UL>
<P><STRONG><a name="[2a6]"></a>lfs_file_flush</STRONG> (Thumb, 232 bytes, Stack size 112 bytes, lfs.o(i.lfs_file_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_seek
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2a5]"></a>lfs_file_outline</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, lfs.o(i.lfs_file_outline))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = lfs_file_outline &rArr; lfs_file_relocate &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2c8]"></a>lfs_file_relocate</STRONG> (Thumb, 232 bytes, Stack size 64 bytes, lfs.o(i.lfs_file_relocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = lfs_file_relocate &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[2b7]"></a>lfs_frombe32</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lfs.o(i.lfs_frombe32))
<BR><BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>

<P><STRONG><a name="[299]"></a>lfs_fromle32</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lfs.o(i.lfs_fromle32))
<BR><BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[2ce]"></a>lfs_fs_demove</STRONG> (Thumb, 70 bytes, Stack size 48 bytes, lfs.o(i.lfs_fs_demove))
<BR><BR>[Stack]<UL><LI>Max Depth = 816<LI>Call Chain = lfs_fs_demove &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmove
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
</UL>

<P><STRONG><a name="[2d0]"></a>lfs_fs_deorphan</STRONG> (Thumb, 284 bytes, Stack size 136 bytes, lfs.o(i.lfs_fs_deorphan))
<BR><BR>[Stack]<UL><LI>Max Depth = 936<LI>Call Chain = lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
</UL>

<P><STRONG><a name="[2ca]"></a>lfs_fs_forceconsistency</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_fs_forceconsistency))
<BR><BR>[Stack]<UL><LI>Max Depth = 944<LI>Call Chain = lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>

<P><STRONG><a name="[2d1]"></a>lfs_fs_parent</STRONG> (Thumb, 96 bytes, Stack size 64 bytes, lfs.o(i.lfs_fs_parent))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = lfs_fs_parent &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
</UL>
<BR>[Called By]<UL><LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
</UL>

<P><STRONG><a name="[b3]"></a>lfs_fs_parent_match</STRONG> (Thumb, 62 bytes, Stack size 40 bytes, lfs.o(i.lfs_fs_parent_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = lfs_fs_parent_match &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_fs_parent)
</UL>
<P><STRONG><a name="[2aa]"></a>lfs_fs_pred</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, lfs.o(i.lfs_fs_pred))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lfs_fs_pred &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2d2]"></a>lfs_fs_preporphans</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lfs.o(i.lfs_fs_preporphans))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_fs_preporphans
</UL>
<BR>[Calls]<UL><LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasorphans
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
</UL>

<P><STRONG><a name="[2bd]"></a>lfs_fs_relocate</STRONG> (Thumb, 222 bytes, Stack size 64 bytes, lfs.o(i.lfs_fs_relocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + In Cycle
<LI>Call Chain = lfs_fs_relocate &rArr;  lfs_dir_commit (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[b4]"></a>lfs_fs_size_count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lfs.o(i.lfs_fs_size_count))
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_fs_size)
</UL>
<P><STRONG><a name="[2b2]"></a>lfs_gstate_fromle32</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_gstate_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2cf]"></a>lfs_gstate_hasmove</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_hasmove))
<BR><BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
</UL>

<P><STRONG><a name="[2a8]"></a>lfs_gstate_hasmovehere</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_hasmovehere))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_gstate_hasmovehere &rArr; lfs_pair_cmp
</UL>
<BR>[Calls]<UL><LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2d3]"></a>lfs_gstate_hasorphans</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_hasorphans))
<BR><BR>[Called By]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
</UL>

<P><STRONG><a name="[2ae]"></a>lfs_gstate_iszero</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_iszero))
<BR><BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2b0]"></a>lfs_gstate_tole32</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_tole32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_gstate_tole32
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
</UL>
<BR>[Called By]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2a9]"></a>lfs_gstate_xormove</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, lfs.o(i.lfs_gstate_xormove))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lfs_gstate_xormove
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2cd]"></a>lfs_init</STRONG> (Thumb, 284 bytes, Stack size 16 bytes, lfs.o(i.lfs_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = lfs_init &rArr; lfs_malloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_malloc
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
</UL>

<P><STRONG><a name="[2cb]"></a>lfs_malloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs.o(i.lfs_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = lfs_malloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
</UL>

<P><STRONG><a name="[287]"></a>lfs_min</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lfs.o(i.lfs_min))
<BR><BR>[Called By]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find_match
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[2a4]"></a>lfs_pair_cmp</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2a7]"></a>lfs_pair_fromle32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[2bc]"></a>lfs_pair_isnull</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lfs.o(i.lfs_pair_isnull))
<BR><BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[2ac]"></a>lfs_pair_tole32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_tole32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_tole32
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[29c]"></a>lfs_popc</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, lfs.o(i.lfs_popc))
<BR><BR>[Called By]<UL><LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
</UL>

<P><STRONG><a name="[2b6]"></a>lfs_tag_dsize</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lfs.o(i.lfs_tag_dsize))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_tag_dsize
</UL>
<BR>[Calls]<UL><LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
</UL>
<BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit_size
</UL>

<P><STRONG><a name="[2c6]"></a>lfs_tag_isdelete</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lfs.o(i.lfs_tag_isdelete))
<BR><BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse_filter
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>

<P><STRONG><a name="[298]"></a>lfs_tole32</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs.o(i.lfs_tole32))
<BR><BR>[Calls]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[b7]"></a>lfs_deskio_erase</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, lfs_port.o(i.lfs_deskio_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_deskio_erase &rArr; spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[b6]"></a>lfs_deskio_prog</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs_port.o(i.lfs_deskio_prog))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = lfs_deskio_prog &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[b5]"></a>lfs_deskio_read</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs_port.o(i.lfs_deskio_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = lfs_deskio_read &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[b8]"></a>lfs_deskio_sync</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs_port.o(i.lfs_deskio_sync))
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[1c6]"></a>WouoUI_BuffWriteByte</STRONG> (Thumb, 62 bytes, Stack size 12 bytes, wououi_graph.o(i.WouoUI_BuffWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = WouoUI_BuffWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
</UL>

<P><STRONG><a name="[1c8]"></a>WouoUI_CanvasWriteByte</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, wououi_graph.o(i.WouoUI_CanvasWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawPoint
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBMP
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
</UL>

<P><STRONG><a name="[1e3]"></a>WouoUI_ListAuotCanvasDrawLineTailValTxt</STRONG> (Thumb, 180 bytes, Stack size 48 bytes, wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[1e4]"></a>WouoUI_ListDrawText_CheckBox</STRONG> (Thumb, 492 bytes, Stack size 80 bytes, wououi_page.o(i.WouoUI_ListDrawText_CheckBox))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = WouoUI_ListDrawText_CheckBox &rArr; WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f_str
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageShow
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIn
</UL>

<P><STRONG><a name="[200]"></a>_WouoUI_WaveAnimParaReaset</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, wououi_page.o(i._WouoUI_WaveAnimParaReaset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _WouoUI_WaveAnimParaReaset
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageInParaInit
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShowNextWaveData
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShowLastWaveData
</UL>

<P><STRONG><a name="[202]"></a>_WouoUI_WaveUpdateRange</STRONG> (Thumb, 362 bytes, Stack size 32 bytes, wououi_page.o(i._WouoUI_WaveUpdateRange))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _WouoUI_WaveUpdateRange
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_roundToNearestTen
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_indexIsInShowRange
</UL>
<BR>[Called By]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageStopRestartWave
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageRightShiftWave
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageLeftShiftWave
</UL>

<P><STRONG><a name="[20a]"></a>_WouoUI_indexIsInShowRange</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, wououi_page.o(i._WouoUI_indexIsInShowRange))
<BR><BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveUpdateRange
</UL>

<P><STRONG><a name="[20b]"></a>_roundToNearestTen</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, wououi_page.o(i._roundToNearestTen))
<BR><BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveUpdateRange
</UL>

<P><STRONG><a name="[1d6]"></a>_WouoUI_ConfWinPageDraw</STRONG> (Thumb, 256 bytes, Stack size 40 bytes, wououi_win.o(i._WouoUI_ConfWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = _WouoUI_ConfWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
</UL>

<P><STRONG><a name="[1f8]"></a>_WouoUI_SpinWinPageDraw</STRONG> (Thumb, 614 bytes, Stack size 64 bytes, wououi_win.o(i._WouoUI_SpinWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = _WouoUI_SpinWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
</UL>

<P><STRONG><a name="[1fe]"></a>_WouoUI_ValWinPageDraw</STRONG> (Thumb, 494 bytes, Stack size 48 bytes, wououi_win.o(i._WouoUI_ValWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = _WouoUI_ValWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
</UL>

<P><STRONG><a name="[1d7]"></a>_WouoUI_WinGetBGSelectItem</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, wououi_win.o(i._WouoUI_WinGetBGSelectItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _WouoUI_WinGetBGSelectItem &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInParaInit
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageInParaInit
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageInParaInit
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageInParaInit
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
</UL>

<P><STRONG><a name="[1eb]"></a>_WouoUI_listWinPageDraw</STRONG> (Thumb, 200 bytes, Stack size 48 bytes, wououi_win.o(i._WouoUI_listWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = _WouoUI_listWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte &rArr; WouoUI_BuffWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
</UL>

<P><STRONG><a name="[282]"></a>generate_sine</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, dac_app.o(i.generate_sine))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = generate_sine &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>
<BR>[Called By]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>

<P><STRONG><a name="[283]"></a>generate_square</STRONG> (Thumb, 116 bytes, Stack size 12 bytes, dac_app.o(i.generate_square))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = generate_square
</UL>
<BR>[Called By]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>

<P><STRONG><a name="[284]"></a>generate_triangle</STRONG> (Thumb, 292 bytes, Stack size 12 bytes, dac_app.o(i.generate_triangle))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = generate_triangle
</UL>
<BR>[Called By]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_waveform
</UL>

<P><STRONG><a name="[247]"></a>generate_waveform</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, dac_app.o(i.generate_waveform))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = generate_waveform &rArr; generate_sine &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_triangle
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_square
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_sine
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
</UL>

<P><STRONG><a name="[248]"></a>start_dac_dma</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dac_app.o(i.start_dac_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = start_dac_dma &rArr; update_timer_frequency &rArr; update_adc_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start_DMA
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
<LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
</UL>

<P><STRONG><a name="[246]"></a>stop_dac_dma</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, dac_app.o(i.stop_dac_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = stop_dac_dma &rArr; HAL_DAC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Stop_DMA
<LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_app_set_amplitude
</UL>

<P><STRONG><a name="[316]"></a>update_adc_timer_frequency</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, dac_app.o(i.update_adc_timer_frequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = update_adc_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_GenerateEvent
<LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timer_frequency
</UL>

<P><STRONG><a name="[2f5]"></a>update_timer_frequency</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dac_app.o(i.update_timer_frequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = update_timer_frequency &rArr; update_adc_timer_frequency &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_GenerateEvent
<LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_adc_timer_frequency
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_dac_dma
</UL>

<P><STRONG><a name="[cd]"></a>cmd_cat</STRONG> (Thumb, 130 bytes, Stack size 192 bytes, shell_app.o(i.cmd_cat))
<BR><BR>[Stack]<UL><LI>Max Depth = 1240<LI>Call Chain = cmd_cat &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[cb]"></a>cmd_cd</STRONG> (Thumb, 300 bytes, Stack size 648 bytes, shell_app.o(i.cmd_cd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1408<LI>Call Chain = cmd_cd &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[d4]"></a>cmd_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, shell_app.o(i.cmd_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 768<LI>Call Chain = cmd_clear &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[d2]"></a>cmd_cp</STRONG> (Thumb, 232 bytes, Stack size 328 bytes, shell_app.o(i.cmd_cp))
<BR><BR>[Stack]<UL><LI>Max Depth = 1376<LI>Call Chain = cmd_cp &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[d3]"></a>cmd_echo</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, shell_app.o(i.cmd_echo))
<BR><BR>[Stack]<UL><LI>Max Depth = 784<LI>Call Chain = cmd_echo &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[c9]"></a>cmd_help</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, shell_app.o(i.cmd_help))
<BR><BR>[Stack]<UL><LI>Max Depth = 776<LI>Call Chain = cmd_help &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[ca]"></a>cmd_ls</STRONG> (Thumb, 602 bytes, Stack size 17960 bytes, shell_app.o(i.cmd_ls))
<BR><BR>[Stack]<UL><LI>Max Depth = 18720<LI>Call Chain = cmd_ls &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[ce]"></a>cmd_mkdir</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, shell_app.o(i.cmd_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 1144<LI>Call Chain = cmd_mkdir &rArr; lfs_mkdir &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mkdir
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[d1]"></a>cmd_mv</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, shell_app.o(i.cmd_mv))
<BR><BR>[Stack]<UL><LI>Max Depth = 1168<LI>Call Chain = cmd_mv &rArr; lfs_rename &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_rename
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[cc]"></a>cmd_pwd</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, shell_app.o(i.cmd_pwd))
<BR><BR>[Stack]<UL><LI>Max Depth = 768<LI>Call Chain = cmd_pwd &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[cf]"></a>cmd_rm</STRONG> (Thumb, 118 bytes, Stack size 72 bytes, shell_app.o(i.cmd_rm))
<BR><BR>[Stack]<UL><LI>Max Depth = 1136<LI>Call Chain = cmd_rm &rArr; lfs_remove &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[d0]"></a>cmd_touch</STRONG> (Thumb, 86 bytes, Stack size 104 bytes, shell_app.o(i.cmd_touch))
<BR><BR>[Stack]<UL><LI>Max Depth = 1152<LI>Call Chain = cmd_touch &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[d5]"></a>cmd_write</STRONG> (Thumb, 176 bytes, Stack size 112 bytes, shell_app.o(i.cmd_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 1160<LI>Call Chain = cmd_write &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Address Reference Count : 1]<UL><LI> shell_app.o(.constdata)
</UL>
<P><STRONG><a name="[2e5]"></a>shell_add_to_history</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, shell_app.o(i.shell_add_to_history))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = shell_add_to_history &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process_char
</UL>

<P><STRONG><a name="[2e8]"></a>shell_handle_history</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, shell_app.o(i.shell_handle_history))
<BR><BR>[Stack]<UL><LI>Max Depth = 776<LI>Call Chain = shell_handle_history &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process_char
</UL>

<P><STRONG><a name="[2e9]"></a>shell_handle_tab</STRONG> (Thumb, 832 bytes, Stack size 9256 bytes, shell_app.o(i.shell_handle_tab))
<BR><BR>[Stack]<UL><LI>Max Depth = 10016<LI>Call Chain = shell_handle_tab &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_close
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_normalize_path
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strrchr
<LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncat
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process_char
</UL>

<P><STRONG><a name="[22f]"></a>shell_normalize_path</STRONG> (Thumb, 268 bytes, Stack size 536 bytes, shell_app.o(i.shell_normalize_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 552<LI>Call Chain = shell_normalize_path &rArr; strtok
</UL>
<BR>[Calls]<UL><LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
</UL>
<BR>[Called By]<UL><LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_write
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_touch
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_rm
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_mv
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_mkdir
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ls
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cp
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cd
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cat
</UL>

<P><STRONG><a name="[2f0]"></a>shell_process_char</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, shell_app.o(i.shell_process_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 10032<LI>Call Chain = shell_process_char &rArr; shell_handle_tab &rArr; shell_printf &rArr; shell_print &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_printf
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_execute
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_tab
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_handle_history
<LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_add_to_history
</UL>
<BR>[Called By]<UL><LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shell_process
</UL>

<P><STRONG><a name="[219]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[20d]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[21c]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[21b]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[aa]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 2]<UL><LI> printfa.o(i.__0snprintf)
<LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[ab]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
