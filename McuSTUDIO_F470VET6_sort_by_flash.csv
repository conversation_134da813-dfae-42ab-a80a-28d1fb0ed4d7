File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.648121%,11739,96,11188,551,0,96
sdio_sdcard.o,10.218440%,6797,61,6792,0,5,56
ff.o,8.402364%,5589,6,5574,15,0,6
oled.o,5.057354%,3364,22,1270,2072,22,0
date_save.o,4.821324%,3207,1101,2236,971,0,1101
usart_app.o,4.499602%,2993,516,2208,785,0,516
date_take.o,3.687779%,2453,35,2356,87,10,25
btod.o,3.235263%,2152,0,2152,0,0,0
ebtn.o,2.522663%,1678,60,1678,0,0,60
fz_wm.l,2.369319%,1576,0,1560,16,0,0
head.o,2.345265%,1560,576,1558,0,2,574
scanf_fp.o,1.912293%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.581551%,1052,0,1052,0,0,0
perf_counter.o,1.464287%,974,76,962,4,8,68
gd32f4xx_rcu.o,1.214727%,808,0,800,8,0,0
m_wm.l,1.205707%,802,0,802,0,0,0
_printf_fp_hex.o,1.205707%,802,0,764,38,0,0
scanf_hexfp.o,1.202700%,800,0,800,0,0,0
syscheck.o,0.984711%,655,0,602,53,0,0
gd32f4xx_dma.o,0.959153%,638,0,638,0,0,0
gd32f4xx_sdio.o,0.914052%,608,0,608,0,0,0
gd32f4xx_timer.o,0.877971%,584,0,584,0,0,0
system_gd32f4xx.o,0.829863%,552,4,548,0,4,0
gd32f4xx_usart.o,0.799796%,532,0,532,0,0,0
gd32f4xx_adc.o,0.781755%,520,0,520,0,0,0
gd32f4xx_fmc.o,0.760708%,506,0,506,0,0,0
startup_gd32f450_470.o,0.739661%,492,2048,64,428,0,2048
btn_app.o,0.715607%,476,196,266,14,196,0
gd32f4xx_rtc.o,0.655472%,436,0,436,0,0,0
gd32f4xx_i2c.o,0.652465%,434,0,434,0,0,0
__printf_flags_ss_wp.o,0.614880%,409,0,392,17,0,0
bigflt0.o,0.565269%,376,0,228,148,0,0
gd25qxx.o,0.535202%,356,0,356,0,0,0
dmul.o,0.511147%,340,0,340,0,0,0
lc_ctype_c.o,0.475067%,316,0,44,272,0,0
scanf_infnan.o,0.463040%,308,0,308,0,0,0
gd32f4xx_it.o,0.432972%,288,5,288,0,0,5
gd32f4xx_gpio.o,0.423952%,282,0,282,0,0,0
narrow.o,0.399898%,266,0,266,0,0,0
lludivv7m.o,0.360810%,240,0,240,0,0,0
diskio.o,0.354797%,236,0,236,0,0,0
ldexp.o,0.342770%,228,0,228,0,0,0
_printf_wctomb.o,0.294662%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.282635%,188,0,148,40,0,0
gd32f4xx_dac.o,0.273614%,182,0,182,0,0,0
_printf_intcommon.o,0.267601%,178,0,178,0,0,0
strtod.o,0.264594%,176,0,176,0,0,0
scheduler.o,0.258581%,172,85,88,0,84,1
led_app.o,0.245050%,163,7,162,0,1,6
gd32f4xx_misc.o,0.243547%,162,0,162,0,0,0
_strtoul.o,0.237533%,158,0,158,0,0,0
dnaninf.o,0.234527%,156,0,156,0,0,0
strncmp.o,0.225506%,150,0,150,0,0,0
frexp.o,0.210473%,140,0,140,0,0,0
fnaninf.o,0.210473%,140,0,140,0,0,0
rt_memcpy_v6.o,0.207466%,138,0,138,0,0,0
lludiv10.o,0.207466%,138,0,138,0,0,0
_printf_fp_infnan.o,0.192432%,128,0,128,0,0,0
perfc_port_default.o,0.189425%,126,0,126,0,0,0
strcmpv7em.o,0.186419%,124,0,124,0,0,0
_printf_longlong_dec.o,0.186419%,124,0,124,0,0,0
dleqf.o,0.180405%,120,0,120,0,0,0
deqf.o,0.180405%,120,0,120,0,0,0
_printf_dec.o,0.180405%,120,0,120,0,0,0
systick.o,0.177398%,118,4,118,0,0,4
llsdiv.o,0.174392%,116,0,116,0,0,0
strtol.o,0.168378%,112,0,112,0,0,0
_printf_oct_int_ll.o,0.168378%,112,0,112,0,0,0
drleqf.o,0.162365%,108,0,108,0,0,0
gd32f4xx_spi.o,0.153344%,102,0,102,0,0,0
retnan.o,0.150338%,100,0,100,0,0,0
rt_memcpy_w.o,0.150338%,100,0,100,0,0,0
d2f.o,0.147331%,98,0,98,0,0,0
__scatter.o,0.141317%,94,0,94,0,0,0
scalbn.o,0.138311%,92,0,92,0,0,0
main.o,0.135304%,90,0,90,0,0,0
memcmp.o,0.132297%,88,0,88,0,0,0
f2d.o,0.129290%,86,0,86,0,0,0
_printf_str.o,0.123277%,82,0,82,0,0,0
rt_memclr_w.o,0.117263%,78,0,78,0,0,0
_printf_pad.o,0.117263%,78,0,78,0,0,0
sys_stackheap_outer.o,0.111250%,74,0,74,0,0,0
lc_numeric_c.o,0.108243%,72,0,44,28,0,0
_c16rtomb.o,0.108243%,72,0,72,0,0,0
rt_memclr.o,0.102230%,68,0,68,0,0,0
dunder.o,0.096216%,64,0,64,0,0,0
_sgetc.o,0.096216%,64,0,64,0,0,0
strlen.o,0.093209%,62,0,62,0,0,0
vsnprintf.o,0.090203%,60,0,60,0,0,0
oled_app.o,0.090203%,60,0,60,0,0,0
__dczerorl.o,0.087196%,58,0,58,0,0,0
atof.o,0.084189%,56,0,56,0,0,0
fpclassify.o,0.072162%,48,0,48,0,0,0
trapv.o,0.072162%,48,0,48,0,0,0
_printf_char_common.o,0.072162%,48,0,48,0,0,0
init_aeabi.o,0.066149%,44,0,44,0,0,0
_printf_wchar.o,0.066149%,44,0,44,0,0,0
_printf_char.o,0.066149%,44,0,44,0,0,0
__2sprintf.o,0.066149%,44,0,44,0,0,0
_printf_charcount.o,0.060135%,40,0,40,0,0,0
dflt_clz.o,0.057128%,38,0,38,0,0,0
llshl.o,0.057128%,38,0,38,0,0,0
libinit2.o,0.057128%,38,0,38,0,0,0
_printf_truncate.o,0.054122%,36,0,36,0,0,0
_chval.o,0.042095%,28,0,28,0,0,0
__scatter_zi.o,0.042095%,28,0,28,0,0,0
systick_wrapper_gnu.o,0.042095%,28,0,28,0,0,0
fpinit.o,0.039088%,26,0,26,0,0,0
atoi.o,0.039088%,26,0,26,0,0,0
dcmpi.o,0.036081%,24,0,24,0,0,0
_rserrno.o,0.033074%,22,0,22,0,0,0
adc_app.o,0.033074%,22,0,22,0,0,0
isspace.o,0.027061%,18,0,18,0,0,0
exit.o,0.027061%,18,0,18,0,0,0
gd32f4xx_pmu.o,0.027061%,18,0,18,0,0,0
fpconst.o,0.024054%,16,0,0,16,0,0
dcheck1.o,0.024054%,16,0,16,0,0,0
rt_ctype_table.o,0.024054%,16,0,16,0,0,0
_snputc.o,0.024054%,16,0,16,0,0,0
__printf_wp.o,0.021047%,14,0,14,0,0,0
dretinf.o,0.018041%,12,0,12,0,0,0
sys_exit.o,0.018041%,12,0,12,0,0,0
__rtentry2.o,0.018041%,12,0,12,0,0,0
rtc_app.o,0.018041%,12,0,12,0,0,0
fretinf.o,0.015034%,10,0,10,0,0,0
rtexit2.o,0.015034%,10,0,10,0,0,0
_sputc.o,0.015034%,10,0,10,0,0,0
_printf_ll.o,0.015034%,10,0,10,0,0,0
_printf_l.o,0.015034%,10,0,10,0,0,0
sd_app.o,0.015034%,10,560,10,0,0,560
scanf2.o,0.012027%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.012027%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.012027%,8,0,8,0,0,0
libspace.o,0.012027%,8,96,8,0,0,96
__main.o,0.012027%,8,0,8,0,0,0
istatus.o,0.009020%,6,0,6,0,0,0
heapauxi.o,0.009020%,6,0,6,0,0,0
_printf_x.o,0.009020%,6,0,6,0,0,0
_printf_u.o,0.009020%,6,0,6,0,0,0
_printf_s.o,0.009020%,6,0,6,0,0,0
_printf_p.o,0.009020%,6,0,6,0,0,0
_printf_o.o,0.009020%,6,0,6,0,0,0
_printf_n.o,0.009020%,6,0,6,0,0,0
_printf_ls.o,0.009020%,6,0,6,0,0,0
_printf_llx.o,0.009020%,6,0,6,0,0,0
_printf_llu.o,0.009020%,6,0,6,0,0,0
_printf_llo.o,0.009020%,6,0,6,0,0,0
_printf_lli.o,0.009020%,6,0,6,0,0,0
_printf_lld.o,0.009020%,6,0,6,0,0,0
_printf_lc.o,0.009020%,6,0,6,0,0,0
_printf_i.o,0.009020%,6,0,6,0,0,0
_printf_g.o,0.009020%,6,0,6,0,0,0
_printf_f.o,0.009020%,6,0,6,0,0,0
_printf_e.o,0.009020%,6,0,6,0,0,0
_printf_d.o,0.009020%,6,0,6,0,0,0
_printf_c.o,0.009020%,6,0,6,0,0,0
_printf_a.o,0.009020%,6,0,6,0,0,0
__rtentry4.o,0.009020%,6,0,6,0,0,0
scanf1.o,0.006014%,4,0,4,0,0,0
printf2.o,0.006014%,4,0,4,0,0,0
printf1.o,0.006014%,4,0,4,0,0,0
_printf_percent_end.o,0.006014%,4,0,4,0,0,0
use_no_semi.o,0.003007%,2,0,2,0,0,0
rtexit.o,0.003007%,2,0,2,0,0,0
libshutdown2.o,0.003007%,2,0,2,0,0,0
libshutdown.o,0.003007%,2,0,2,0,0,0
libinit.o,0.003007%,2,0,2,0,0,0
